# Generic Stateful Routing Implementation

## Overview

I have successfully refactored the collection-aware routing implementation to create a **generic stateful routing pattern** at the BaseService level. This makes stateful routing reusable across all services while maintaining the VectorDB collection-aware functionality.

## Key Improvements

### 🔧 **Generic Implementation at BaseService Level**

**Before**: Collection-specific routing logic was embedded in VectorDBService
**After**: Generic stateful routing is available to all services through BaseService

### 🎯 **Reusable Pattern**

Any service can now enable stateful routing by:
1. Setting `stateful_routing=True` in constructor
2. Overriding `extract_hash_key()` method to define hash key extraction logic
3. Choosing an assignment strategy (consistent_hashing, load_balanced, explicit)

## Implementation Details

### 1. **Enhanced BaseServiceRequest**

Added optional `hash_key` field for explicit stateful routing:

```python
class BaseServiceRequest(BaseModel):
    service_name: str
    operation_name: str
    raw_data: Dict = Field(default_factory=dict)
    hash_key: Optional[str] = None  # NEW: For stateful routing
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = Field(default_factory=dict)
```

### 2. **Enhanced BaseService Constructor**

Added stateful routing parameters:

```python
class BaseService:
    def __init__(
        self, 
        service_name: str, 
        scheduling_policy: SchedulingPolicyType = "round_robin",
        stateful_routing: bool = False,  # NEW
        stateful_assignment_strategy: StatefulAssignmentStrategyType = "consistent_hashing"  # NEW
    ):
```

### 3. **Generic Stateful Routing State**

Added state tracking for stateful routing:

```python
# Stateful routing state
self.hash_key_to_instance: Dict[str, str] = {}
self.instance_hash_key_count: Dict[str, int] = {}
```

### 4. **Hash Key Extraction Interface**

Defined extensible interface for hash key extraction:

```python
def extract_hash_key(self, request: BaseServiceRequest) -> Optional[str]:
    """Extract hash key from request for stateful routing.
    
    This method should be overridden by subclasses to define their
    specific hash key extraction logic (e.g., collection_name for VDB,
    user_id for user services, etc.).
    """
    return request.hash_key
```

### 5. **Assignment Strategies**

Implemented three assignment strategies:

#### Consistent Hashing
```python
def _assign_by_consistent_hashing(self, hash_key: str, candidate_instances: List[str]) -> str:
    """Assign hash key using consistent hashing for deterministic assignment."""
    import hashlib
    key_hash = hashlib.md5(hash_key.encode()).hexdigest()
    hash_int = int(key_hash, 16)
    instance_index = hash_int % len(candidate_instances)
    return candidate_instances[instance_index]
```

#### Load Balanced
```python
def _assign_by_load_balancing(self, hash_key: str, candidate_instances: List[str]) -> str:
    """Assign hash key to the instance with the fewest hash keys."""
    min_count = float('inf')
    selected_instance = candidate_instances[0]
    for instance_name in candidate_instances:
        count = self.instance_hash_key_count.get(instance_name, 0)
        if count < min_count:
            min_count = count
            selected_instance = instance_name
    return selected_instance
```

#### Explicit
Manual assignment with fallback to round-robin for unassigned keys.

### 6. **Enhanced Schedule Method**

Updated scheduling to support stateful routing:

```python
def schedule(self, request: BaseServiceRequest, instance_name: str = None, tags: Optional[FrozenSet[str]] = None) -> BaseServiceInstance:
    # If instance_name is explicitly provided, use it
    if instance_name is not None:
        return self.instances[instance_name]

    # Check for stateful routing
    if self.stateful_routing:
        hash_key = self.extract_hash_key(request)
        if hash_key:
            # Route to assigned instance or assign new instance
            # ... (stateful routing logic)
    
    # Fall back to default scheduling
    # ... (normal scheduling logic)
```

### 7. **Management API**

Added generic management methods:

```python
def get_hash_key_mapping(self) -> Dict[str, str]
def set_hash_key_mapping(self, hash_key: str, instance_name: str) -> None
def clear_hash_key_mapping(self, hash_key: str) -> None
def get_instance_hash_key_count(self) -> Dict[str, int]
```

## Refactored VectorDBService

### 1. **Simplified Constructor**

```python
class VectorDBService(BaseService):
    def __init__(self, 
                 scheduling_policy: str = "round_robin",
                 collection_assignment_strategy: str = "consistent_hashing"):
        # Enable stateful routing with collection-aware strategy
        super().__init__(
            service_name="vdb", 
            scheduling_policy=scheduling_policy,
            stateful_routing=True,  # Automatically enabled
            stateful_assignment_strategy=collection_assignment_strategy
        )
```

### 2. **Collection-Specific Hash Key Extraction**

```python
def extract_hash_key(self, request: BaseServiceRequest) -> Optional[str]:
    """Extract collection name from VDB request for stateful routing."""
    # First check if hash_key is explicitly set
    if request.hash_key:
        return request.hash_key
        
    # Extract collection_name from raw_data for VDB requests
    if isinstance(request, VectorDBServiceRequest):
        raw_data = request.raw_data or {}
        return raw_data.get("collection_name")
        
    return None
```

### 3. **Backward Compatibility**

Maintained existing VDB-specific API:

```python
def get_collection_mapping(self) -> Dict[str, str]:
    """Backward compatibility: delegate to generic method."""
    return self.get_hash_key_mapping()

def set_collection_mapping(self, collection_name: str, instance_name: str) -> None:
    """Backward compatibility: delegate to generic method."""
    self.set_hash_key_mapping(collection_name, instance_name)

def clear_collection_mapping(self, collection_name: str) -> None:
    """Backward compatibility: delegate to generic method."""
    self.clear_hash_key_mapping(collection_name)
```

## Usage Examples

### 1. **Generic User Service**

```python
class UserService(BaseService):
    def __init__(self):
        super().__init__(
            service_name="user_service",
            stateful_routing=True,
            stateful_assignment_strategy="consistent_hashing"
        )
    
    def extract_hash_key(self, request: BaseServiceRequest) -> Optional[str]:
        """Extract user_id for stateful routing."""
        if request.hash_key:
            return request.hash_key
        raw_data = request.raw_data or {}
        return raw_data.get("user_id")

# Usage
user_service = UserService()
request = BaseServiceRequest(
    service_name="user_service",
    operation_name="process",
    raw_data={"user_id": "alice", "action": "login"}
)
instance = user_service.schedule(request)  # Always routes alice to same instance
```

### 2. **VectorDB Service (Unchanged API)**

```python
# Existing code continues to work
vdb_service = VectorDBService()
request = VectorDBServiceRequest(
    service_name="vdb",
    operation_name="store",
    raw_data={"collection_name": "docs", "texts": ["hello"], "embeddings": [[0.1]*10]}
)
instance = vdb_service.schedule(request)  # Routes based on collection_name
```

### 3. **Explicit Hash Key**

```python
# Any service can use explicit hash_key
request = BaseServiceRequest(
    service_name="any_service",
    operation_name="process",
    hash_key="explicit_routing_key",
    raw_data={"other": "data"}
)
```

## Benefits

### 🎯 **Reusability**
- Any service can enable stateful routing with minimal code
- Consistent pattern across all services
- Reduces code duplication

### 🔧 **Flexibility**
- Multiple assignment strategies
- Service-specific hash key extraction
- Explicit hash key override capability

### 🔄 **Backward Compatibility**
- Existing VectorDB code continues to work unchanged
- VDB-specific methods delegate to generic implementation
- No breaking changes

### 📈 **Scalability**
- Generic pattern scales to any number of services
- Consistent behavior across service types
- Easy to add new stateful services

### 🛡️ **Consistency**
- Same hash key always routes to same instance
- Data consistency maintained across operations
- Handles instance addition/removal gracefully

## Testing

Created comprehensive test suite (`tests/unit/test_stateful_routing.py`) covering:
- Generic stateful routing functionality
- Hash key assignment strategies
- Instance management
- VectorDB-specific functionality
- Backward compatibility

## Conclusion

The generic stateful routing implementation successfully:

1. **Generalizes the pattern**: Moves stateful routing from VectorDB-specific to BaseService-level
2. **Maintains functionality**: All VectorDB collection-aware routing features preserved
3. **Enables reusability**: Any service can now use stateful routing
4. **Preserves compatibility**: Existing code continues to work unchanged
5. **Provides flexibility**: Multiple strategies and service-specific customization

This creates a robust, reusable foundation for stateful routing that can be leveraged by any service requiring consistent request routing based on hash keys.
