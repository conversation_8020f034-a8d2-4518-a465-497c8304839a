"""
Test generic stateful routing and VectorDB collection-aware routing.

This module tests the generic stateful routing functionality in BaseService
and the VectorDB-specific collection-to-instance mapping functionality
to ensure data consistency across stateful operations.
"""

import pytest
from unittest.mock import MagicMock, patch
from typing import Dict, List, Optional

from lego_agent.core.service.base import (
    BaseService,
    BaseServiceRequest,
    BaseServiceResponse,
    BaseServiceInstance,
    BaseServiceInstanceConfig,
)
from lego_agent.core.service.vdb import (
    VectorDBService,
    VectorDBServiceRequest,
    VectorDBServiceResponse,
    VectorDBServiceInstance,
    VectorDBServiceInstanceConfig,
)


class MockServiceInstance(BaseServiceInstance):
    """Mock service instance for testing generic stateful routing."""

    def __init__(
        self, instance_name: str, group_name: str, config: BaseServiceInstanceConfig
    ):
        super().__init__("test_service", instance_name, group_name, config)
        self.served_requests = []

    def launch(self):
        pass

    def shutdown(self):
        pass

    def _serve_impl(self, request: BaseServiceRequest) -> BaseServiceResponse:
        self.served_requests.append(request)
        return BaseServiceResponse(
            service_name=self.service_name,
            instance_name=self.instance_name,
            operation_name=request.operation_name,
            raw_data={"result": f"served by {self.instance_name}"},
            request_id=request.request_id,
        )

    async def _serve_async_impl(
        self, request: BaseServiceRequest
    ) -> BaseServiceResponse:
        return self._serve_impl(request)


class TestStatefulService(BaseService):
    """Test service that uses stateful routing with custom hash key extraction."""

    def __init__(self, hash_key_field: str = "user_id", **kwargs):
        super().__init__(service_name="test_stateful", stateful_routing=True, **kwargs)
        self.hash_key_field = hash_key_field

    def extract_hash_key(self, request: BaseServiceRequest) -> Optional[str]:
        """Extract hash key from request based on configured field."""
        # First check if hash_key is explicitly set
        if request.hash_key:
            return request.hash_key

        # Extract from raw_data based on configured field
        raw_data = request.raw_data or {}
        return raw_data.get(self.hash_key_field)


@pytest.fixture
def mock_instances():
    """Create mock service instances for testing."""
    config1 = BaseServiceInstanceConfig(tags=frozenset(["instance1"]))
    config2 = BaseServiceInstanceConfig(tags=frozenset(["instance2"]))
    config3 = BaseServiceInstanceConfig(tags=frozenset(["instance3"]))

    instance1 = MockServiceInstance("instance1", "group1", config1)
    instance2 = MockServiceInstance("instance2", "group1", config2)
    instance3 = MockServiceInstance("instance3", "group1", config3)

    return {"instance1": instance1, "instance2": instance2, "instance3": instance3}


class TestGenericStatefulRouting:
    """Test generic stateful routing functionality in BaseService."""

    def test_stateful_routing_disabled_by_default(self):
        """Test that stateful routing is disabled by default."""
        service = BaseService("test_service")
        assert service.stateful_routing is False
        assert service.hash_key_to_instance == {}
        assert service.instance_hash_key_count == {}

    def test_stateful_routing_enabled(self):
        """Test that stateful routing can be enabled."""
        service = BaseService("test_service", stateful_routing=True)
        assert service.stateful_routing is True
        assert service.stateful_assignment_strategy == "consistent_hashing"
        assert service.hash_key_to_instance == {}
        assert service.instance_hash_key_count == {}

    def test_hash_key_assignment_consistent_hashing(self, mock_instances):
        """Test that hash keys are consistently assigned using hashing."""
        service = TestStatefulService(
            hash_key_field="user_id", stateful_assignment_strategy="consistent_hashing"
        )

        # Add instances
        for name, instance in mock_instances.items():
            service.add_instance(name, instance)

        # Test multiple requests with same hash key go to same instance
        hash_key = "user123"

        requests = [
            BaseServiceRequest(
                service_name="test_stateful",
                operation_name="process",
                raw_data={"user_id": hash_key, "data": f"request{i}"},
            )
            for i in range(5)
        ]

        # All requests should go to the same instance
        selected_instances = []
        for request in requests:
            instance = service.schedule(request)
            selected_instances.append(instance.instance_name)

        # All should be the same
        assert (
            len(set(selected_instances)) == 1
        ), f"Requests routed to different instances: {selected_instances}"

        # Verify the hash key is mapped
        mapping = service.get_hash_key_mapping()
        assert hash_key in mapping
        assert mapping[hash_key] == selected_instances[0]

    def test_different_hash_keys_can_go_to_different_instances(self, mock_instances):
        """Test that different hash keys can be assigned to different instances."""
        service = TestStatefulService(
            hash_key_field="user_id", stateful_assignment_strategy="consistent_hashing"
        )

        # Add instances
        for name, instance in mock_instances.items():
            service.add_instance(name, instance)

        # Create requests for different hash keys
        request1 = BaseServiceRequest(
            service_name="test_stateful",
            operation_name="process",
            raw_data={"user_id": "user123", "data": "request1"},
        )

        request2 = BaseServiceRequest(
            service_name="test_stateful",
            operation_name="process",
            raw_data={"user_id": "user456", "data": "request2"},
        )

        # Schedule both requests
        instance1 = service.schedule(request1)
        instance2 = service.schedule(request2)

        # Verify mapping exists for both
        mapping = service.get_hash_key_mapping()
        assert "user123" in mapping
        assert "user456" in mapping

        # Hash keys should be consistently mapped
        assert mapping["user123"] == instance1.instance_name
        assert mapping["user456"] == instance2.instance_name

    def test_explicit_hash_key_mapping(self, mock_instances):
        """Test explicit hash key-to-instance mapping."""
        service = TestStatefulService(
            hash_key_field="user_id", stateful_assignment_strategy="explicit"
        )

        # Add instances
        for name, instance in mock_instances.items():
            service.add_instance(name, instance)

        hash_key = "user123"
        target_instance = "instance2"

        # Explicitly map hash key to instance
        service.set_hash_key_mapping(hash_key, target_instance)

        # Create request for this hash key
        request = BaseServiceRequest(
            service_name="test_stateful",
            operation_name="process",
            raw_data={"user_id": hash_key, "data": "test"},
        )

        # Should go to the explicitly mapped instance
        instance = service.schedule(request)
        assert instance.instance_name == target_instance

        # Verify mapping
        mapping = service.get_hash_key_mapping()
        assert mapping[hash_key] == target_instance

    def test_load_balanced_assignment(self, mock_instances):
        """Test load-balanced hash key assignment."""
        service = TestStatefulService(
            hash_key_field="user_id", stateful_assignment_strategy="load_balanced"
        )

        # Add instances
        for name, instance in mock_instances.items():
            service.add_instance(name, instance)

        # Create multiple hash keys
        hash_keys = [f"user{i}" for i in range(6)]

        for hash_key in hash_keys:
            request = BaseServiceRequest(
                service_name="test_stateful",
                operation_name="process",
                raw_data={"user_id": hash_key, "data": "test"},
            )
            service.schedule(request)

        # Check that hash keys are distributed
        mapping = service.get_hash_key_mapping()
        instance_counts = {}
        for hash_key, instance in mapping.items():
            instance_counts[instance] = instance_counts.get(instance, 0) + 1

        # Should have some distribution (not all on one instance)
        assert len(instance_counts) > 1 or len(hash_keys) == 1

    def test_instance_removal_handling(self, mock_instances):
        """Test handling of instance removal with assigned hash keys."""
        service = TestStatefulService(hash_key_field="user_id")

        # Add instances
        for name, instance in mock_instances.items():
            service.add_instance(name, instance)

        # Assign a hash key to instance1
        hash_key = "user123"
        service.set_hash_key_mapping(hash_key, "instance1")

        # Verify mapping exists
        mapping = service.get_hash_key_mapping()
        assert mapping[hash_key] == "instance1"

        # Remove instance1
        service.remove_instance("instance1")

        # Mapping should be cleared
        mapping = service.get_hash_key_mapping()
        assert hash_key not in mapping

        # New request should go to remaining instance
        request = BaseServiceRequest(
            service_name="test_stateful",
            operation_name="process",
            raw_data={"user_id": hash_key, "data": "test"},
        )

        instance = service.schedule(request)
        assert instance.instance_name in ["instance2", "instance3"]

    def test_fallback_to_normal_scheduling_without_hash_key(self, mock_instances):
        """Test that requests without hash keys fall back to normal scheduling."""
        service = TestStatefulService(hash_key_field="user_id")

        # Add instances
        for name, instance in mock_instances.items():
            service.add_instance(name, instance)

        # Create request without hash key
        request = BaseServiceRequest(
            service_name="test_stateful",
            operation_name="process",
            raw_data={"data": "test"},  # No user_id field
        )

        # Should still work with normal scheduling
        instance = service.schedule(request)
        assert instance.instance_name in ["instance1", "instance2", "instance3"]

        # No hash key mapping should be created
        mapping = service.get_hash_key_mapping()
        assert len(mapping) == 0


class TestVectorDBStatefulRouting:
    """Test VectorDB-specific stateful routing functionality."""

    def test_vdb_service_uses_stateful_routing(self):
        """Test that VectorDBService enables stateful routing by default."""
        service = VectorDBService()
        assert service.stateful_routing is True
        assert service.stateful_assignment_strategy == "consistent_hashing"

    def test_vdb_extract_hash_key_from_collection_name(self):
        """Test that VectorDBService extracts collection_name as hash key."""
        service = VectorDBService()

        # Test with VectorDBServiceRequest
        request = VectorDBServiceRequest(
            service_name="vdb",
            operation_name="store",
            raw_data={
                "collection_name": "test_collection",
                "texts": ["hello"],
                "embeddings": [[0.1] * 10],
            },
        )

        hash_key = service.extract_hash_key(request)
        assert hash_key == "test_collection"

    def test_vdb_extract_hash_key_from_explicit_hash_key(self):
        """Test that VectorDBService uses explicit hash_key if provided."""
        service = VectorDBService()

        # Test with explicit hash_key
        request = VectorDBServiceRequest(
            service_name="vdb",
            operation_name="store",
            hash_key="explicit_collection",
            raw_data={
                "collection_name": "test_collection",
                "texts": ["hello"],
                "embeddings": [[0.1] * 10],
            },
        )

        hash_key = service.extract_hash_key(request)
        assert hash_key == "explicit_collection"  # Should prefer explicit hash_key

    def test_vdb_backward_compatibility_methods(self):
        """Test that VectorDBService provides backward compatibility methods."""
        service = VectorDBService()

        # Test that collection-specific methods delegate to generic methods
        assert hasattr(service, "get_collection_mapping")
        assert hasattr(service, "set_collection_mapping")
        assert hasattr(service, "clear_collection_mapping")

        # These should be equivalent to the generic methods
        assert service.get_collection_mapping() == service.get_hash_key_mapping()
