"""
Benchmarking system for legoAgent classification workflows.

This package provides a comprehensive benchmarking framework for evaluating
the performance of classification workflows with support for:
- Multi-dataset benchmarking per workflow
- Synthetic dataset generation
- Configurable performance metrics
- Async execution support
- Service mocking for reproducible results
- Performance regression detection
"""

from benchmarks.core.benchmark_runner import Benchmark<PERSON>unner
from benchmarks.core.dataset_manager import DatasetManager
from benchmarks.core.synthetic_generator import SyntheticDatasetGenerator
from benchmarks.core.metrics_collector import MetricsCollector
from benchmarks.workflows.classification_benchmark import ClassificationBenchmark

__all__ = [
    "BenchmarkRunner",
    "DatasetManager", 
    "SyntheticDatasetGenerator",
    "MetricsCollector",
    "ClassificationBenchmark",
]

__version__ = "1.0.0"
