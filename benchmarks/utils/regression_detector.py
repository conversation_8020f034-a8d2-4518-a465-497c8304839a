"""
Performance regression detection for benchmarking system.

This module provides functionality for detecting performance regressions
by comparing current benchmark results with historical baselines.
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import statistics


class RegressionSeverity(Enum):
    """Severity levels for performance regressions."""
    MINOR = "minor"
    MODERATE = "moderate"
    SEVERE = "severe"
    CRITICAL = "critical"


@dataclass
class RegressionAlert:
    """Represents a performance regression alert."""
    metric_name: str
    current_value: float
    baseline_value: float
    change_percent: float
    severity: RegressionSeverity
    threshold_exceeded: float
    description: str


@dataclass
class RegressionReport:
    """Complete regression analysis report."""
    benchmark_name: str
    comparison_timestamp: str
    baseline_path: str
    total_metrics_compared: int
    regressions_found: int
    improvements_found: int
    alerts: List[RegressionAlert]
    summary: Dict[str, Any]


class RegressionDetector:
    """Detects performance regressions by comparing benchmark results."""
    
    def __init__(
        self,
        minor_threshold: float = 0.05,    # 5%
        moderate_threshold: float = 0.15,  # 15%
        severe_threshold: float = 0.30,    # 30%
        critical_threshold: float = 0.50   # 50%
    ):
        """Initialize the regression detector.
        
        Args:
            minor_threshold: Threshold for minor regressions
            moderate_threshold: Threshold for moderate regressions
            severe_threshold: Threshold for severe regressions
            critical_threshold: Threshold for critical regressions
        """
        self.minor_threshold = minor_threshold
        self.moderate_threshold = moderate_threshold
        self.severe_threshold = severe_threshold
        self.critical_threshold = critical_threshold
        
        self.logger = logging.getLogger(__name__)
    
    def detect_regressions(
        self,
        current_results: Dict[str, Any],
        baseline_results: Dict[str, Any],
        metric_weights: Optional[Dict[str, float]] = None
    ) -> RegressionReport:
        """Detect regressions between current and baseline results.
        
        Args:
            current_results: Current benchmark results
            baseline_results: Baseline benchmark results
            metric_weights: Optional weights for different metrics
            
        Returns:
            Regression analysis report
        """
        alerts = []
        improvements = []
        
        current_metrics = current_results.get("metrics", {})
        baseline_metrics = baseline_results.get("metrics", {})
        
        # Compare each metric
        for metric_name in current_metrics.keys():
            if metric_name not in baseline_metrics:
                self.logger.warning(f"Metric '{metric_name}' not found in baseline")
                continue
            
            alert = self._compare_metric(
                metric_name,
                current_metrics[metric_name],
                baseline_metrics[metric_name]
            )
            
            if alert:
                if alert.change_percent > 0:  # Regression
                    alerts.append(alert)
                else:  # Improvement
                    improvements.append(alert)
        
        # Create summary
        summary = self._create_summary(alerts, improvements, metric_weights)
        
        # Create report
        report = RegressionReport(
            benchmark_name=current_results.get("benchmark_info", {}).get("name", "Unknown"),
            comparison_timestamp=current_results.get("benchmark_info", {}).get("timestamp", "Unknown"),
            baseline_path="baseline",  # This would be the actual baseline path
            total_metrics_compared=len(current_metrics),
            regressions_found=len(alerts),
            improvements_found=len(improvements),
            alerts=alerts + improvements,  # Include both regressions and improvements
            summary=summary
        )
        
        return report
    
    def _compare_metric(
        self,
        metric_name: str,
        current_metric: Dict[str, Any],
        baseline_metric: Dict[str, Any]
    ) -> Optional[RegressionAlert]:
        """Compare a single metric between current and baseline.
        
        Args:
            metric_name: Name of the metric
            current_metric: Current metric data
            baseline_metric: Baseline metric data
            
        Returns:
            Regression alert if significant change detected, None otherwise
        """
        # Get mean values for comparison
        current_mean = current_metric.get("mean", 0.0)
        baseline_mean = baseline_metric.get("mean", 0.0)
        
        if baseline_mean == 0:
            self.logger.warning(f"Baseline mean is zero for metric '{metric_name}'")
            return None
        
        # Calculate percentage change
        change_percent = ((current_mean - baseline_mean) / baseline_mean)
        
        # Determine if this is a regression based on metric type
        is_regression = self._is_regression(metric_name, change_percent)
        
        if not is_regression:
            return None
        
        # Determine severity
        abs_change = abs(change_percent)
        severity = self._determine_severity(abs_change)
        
        # Only create alert if change exceeds minor threshold
        if abs_change < self.minor_threshold:
            return None
        
        # Create description
        direction = "increased" if change_percent > 0 else "decreased"
        description = f"{metric_name} {direction} by {abs_change*100:.1f}% (from {baseline_mean:.4f} to {current_mean:.4f})"
        
        return RegressionAlert(
            metric_name=metric_name,
            current_value=current_mean,
            baseline_value=baseline_mean,
            change_percent=change_percent * 100,  # Convert to percentage
            severity=severity,
            threshold_exceeded=abs_change,
            description=description
        )
    
    def _is_regression(self, metric_name: str, change_percent: float) -> bool:
        """Determine if a change represents a regression for the given metric.
        
        Args:
            metric_name: Name of the metric
            change_percent: Percentage change (positive = increase)
            
        Returns:
            True if this represents a regression
        """
        # For latency and error_rate metrics, increases are regressions
        if metric_name.lower() in ["latency", "error_rate", "memory_usage", "cpu_usage"]:
            return change_percent > self.minor_threshold
        
        # For throughput and accuracy metrics, decreases are regressions
        elif metric_name.lower() in ["throughput", "accuracy"]:
            return change_percent < -self.minor_threshold
        
        # For unknown metrics, consider significant changes in either direction
        else:
            return abs(change_percent) > self.minor_threshold
    
    def _determine_severity(self, abs_change_percent: float) -> RegressionSeverity:
        """Determine the severity of a regression.
        
        Args:
            abs_change_percent: Absolute percentage change
            
        Returns:
            Regression severity level
        """
        if abs_change_percent >= self.critical_threshold:
            return RegressionSeverity.CRITICAL
        elif abs_change_percent >= self.severe_threshold:
            return RegressionSeverity.SEVERE
        elif abs_change_percent >= self.moderate_threshold:
            return RegressionSeverity.MODERATE
        else:
            return RegressionSeverity.MINOR
    
    def _create_summary(
        self,
        regressions: List[RegressionAlert],
        improvements: List[RegressionAlert],
        metric_weights: Optional[Dict[str, float]]
    ) -> Dict[str, Any]:
        """Create a summary of the regression analysis.
        
        Args:
            regressions: List of regression alerts
            improvements: List of improvement alerts
            metric_weights: Optional metric weights
            
        Returns:
            Summary dictionary
        """
        # Count by severity
        severity_counts = {severity.value: 0 for severity in RegressionSeverity}
        for alert in regressions:
            severity_counts[alert.severity.value] += 1
        
        # Calculate weighted score if weights provided
        weighted_score = None
        if metric_weights:
            weighted_score = self._calculate_weighted_score(regressions, metric_weights)
        
        # Find most significant regression
        most_significant = None
        if regressions:
            most_significant = max(regressions, key=lambda x: abs(x.change_percent))
        
        # Find best improvement
        best_improvement = None
        if improvements:
            best_improvement = max(improvements, key=lambda x: abs(x.change_percent))
        
        summary = {
            "overall_status": self._determine_overall_status(regressions),
            "severity_breakdown": severity_counts,
            "total_regressions": len(regressions),
            "total_improvements": len(improvements),
            "weighted_score": weighted_score,
            "most_significant_regression": {
                "metric": most_significant.metric_name,
                "change_percent": most_significant.change_percent,
                "severity": most_significant.severity.value
            } if most_significant else None,
            "best_improvement": {
                "metric": best_improvement.metric_name,
                "change_percent": abs(best_improvement.change_percent),
                "description": best_improvement.description
            } if best_improvement else None
        }
        
        return summary
    
    def _calculate_weighted_score(
        self,
        regressions: List[RegressionAlert],
        metric_weights: Dict[str, float]
    ) -> float:
        """Calculate a weighted regression score.
        
        Args:
            regressions: List of regression alerts
            metric_weights: Weights for different metrics
            
        Returns:
            Weighted regression score
        """
        total_score = 0.0
        total_weight = 0.0
        
        for alert in regressions:
            weight = metric_weights.get(alert.metric_name, 1.0)
            score = abs(alert.change_percent) * weight
            total_score += score
            total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else 0.0
    
    def _determine_overall_status(self, regressions: List[RegressionAlert]) -> str:
        """Determine the overall status based on regressions.
        
        Args:
            regressions: List of regression alerts
            
        Returns:
            Overall status string
        """
        if not regressions:
            return "PASS"
        
        # Check for critical or severe regressions
        for alert in regressions:
            if alert.severity == RegressionSeverity.CRITICAL:
                return "CRITICAL"
            elif alert.severity == RegressionSeverity.SEVERE:
                return "SEVERE"
        
        # Check for moderate regressions
        moderate_count = sum(1 for alert in regressions if alert.severity == RegressionSeverity.MODERATE)
        if moderate_count > 2:  # Multiple moderate regressions
            return "MODERATE"
        elif moderate_count > 0:
            return "WARNING"
        
        # Only minor regressions
        return "MINOR"
    
    def save_baseline(
        self,
        results: Dict[str, Any],
        baseline_path: Path
    ):
        """Save benchmark results as a new baseline.
        
        Args:
            results: Benchmark results to save
            baseline_path: Path to save baseline file
        """
        baseline_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(baseline_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        self.logger.info(f"Saved baseline to: {baseline_path}")
    
    def load_baseline(self, baseline_path: Path) -> Optional[Dict[str, Any]]:
        """Load baseline results from file.
        
        Args:
            baseline_path: Path to baseline file
            
        Returns:
            Baseline results or None if not found
        """
        if not baseline_path.exists():
            self.logger.warning(f"Baseline file not found: {baseline_path}")
            return None
        
        try:
            with open(baseline_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load baseline: {e}")
            return None
    
    def generate_regression_report_html(self, report: RegressionReport) -> str:
        """Generate an HTML report for regression analysis.
        
        Args:
            report: Regression report
            
        Returns:
            HTML report content
        """
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Regression Report: {report.benchmark_name}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .status-pass {{ color: #4caf50; }}
        .status-warning {{ color: #ff9800; }}
        .status-severe {{ color: #f44336; }}
        .status-critical {{ color: #d32f2f; font-weight: bold; }}
        .alert-table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
        .alert-table th, .alert-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        .alert-table th {{ background-color: #f2f2f2; }}
        .severity-critical {{ background-color: #ffebee; }}
        .severity-severe {{ background-color: #fff3e0; }}
        .severity-moderate {{ background-color: #fffde7; }}
        .severity-minor {{ background-color: #f3e5f5; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Regression Report: {report.benchmark_name}</h1>
        <p><strong>Status:</strong> <span class="status-{report.summary['overall_status'].lower()}">{report.summary['overall_status']}</span></p>
        <p><strong>Timestamp:</strong> {report.comparison_timestamp}</p>
        <p><strong>Metrics Compared:</strong> {report.total_metrics_compared}</p>
        <p><strong>Regressions Found:</strong> {report.regressions_found}</p>
        <p><strong>Improvements Found:</strong> {report.improvements_found}</p>
    </div>
        """
        
        if report.alerts:
            html += """
    <h2>Detailed Alerts</h2>
    <table class="alert-table">
        <thead>
            <tr>
                <th>Metric</th>
                <th>Current</th>
                <th>Baseline</th>
                <th>Change %</th>
                <th>Severity</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            """
            
            for alert in sorted(report.alerts, key=lambda x: x.severity.value, reverse=True):
                severity_class = f"severity-{alert.severity.value}"
                html += f"""
            <tr class="{severity_class}">
                <td>{alert.metric_name}</td>
                <td>{alert.current_value:.4f}</td>
                <td>{alert.baseline_value:.4f}</td>
                <td>{alert.change_percent:+.1f}%</td>
                <td>{alert.severity.value.upper()}</td>
                <td>{alert.description}</td>
            </tr>
                """
            
            html += """
        </tbody>
    </table>
            """
        
        html += """
</body>
</html>
        """
        
        return html
