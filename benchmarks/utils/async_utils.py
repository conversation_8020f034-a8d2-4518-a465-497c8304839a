"""
Async utilities for benchmarking system.

This module provides utility functions and classes for handling async
operations in benchmarks, including rate limiting and concurrency control.
"""

import asyncio
import time
import random
from typing import List, Callable, Any, Optional
from dataclasses import dataclass


@dataclass
class RateLimiter:
    """Rate limiter for controlling request frequency."""
    
    rate: float  # requests per second
    last_request_time: float = 0.0
    
    async def acquire(self):
        """Acquire permission to make a request."""
        if self.rate <= 0:
            return
        
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        min_interval = 1.0 / self.rate
        
        if time_since_last < min_interval:
            sleep_time = min_interval - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()


class AsyncBenchmarkUtils:
    """Utility class for async benchmark operations."""
    
    @staticmethod
    async def execute_with_rate_limit(
        tasks: List[Callable],
        rate_limit: float,
        concurrency_limit: int = 10
    ) -> List[Any]:
        """Execute tasks with rate limiting and concurrency control.
        
        Args:
            tasks: List of async callable tasks
            rate_limit: Maximum requests per second
            concurrency_limit: Maximum concurrent tasks
            
        Returns:
            List of task results
        """
        rate_limiter = RateLimiter(rate_limit)
        semaphore = asyncio.Semaphore(concurrency_limit)
        
        async def execute_task(task):
            async with semaphore:
                await rate_limiter.acquire()
                return await task()
        
        return await asyncio.gather(*[execute_task(task) for task in tasks])
    
    @staticmethod
    async def execute_with_poisson_arrival(
        tasks: List[Callable],
        average_rate: float,
        concurrency_limit: int = 10
    ) -> List[Any]:
        """Execute tasks with Poisson arrival pattern.
        
        Args:
            tasks: List of async callable tasks
            average_rate: Average requests per second
            concurrency_limit: Maximum concurrent tasks
            
        Returns:
            List of task results
        """
        semaphore = asyncio.Semaphore(concurrency_limit)
        results = []
        
        async def execute_task(task):
            async with semaphore:
                return await task()
        
        # Schedule tasks with Poisson intervals
        task_futures = []
        for task in tasks:
            # Generate Poisson interval
            interval = random.expovariate(average_rate)
            await asyncio.sleep(interval)
            
            future = asyncio.create_task(execute_task(task))
            task_futures.append(future)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*task_futures)
        return results
    
    @staticmethod
    async def execute_with_burst_pattern(
        tasks: List[Callable],
        burst_size: int,
        burst_interval: float,
        concurrency_limit: int = 10
    ) -> List[Any]:
        """Execute tasks in bursts.
        
        Args:
            tasks: List of async callable tasks
            burst_size: Number of tasks per burst
            burst_interval: Time between bursts in seconds
            concurrency_limit: Maximum concurrent tasks
            
        Returns:
            List of task results
        """
        semaphore = asyncio.Semaphore(concurrency_limit)
        results = []
        
        async def execute_task(task):
            async with semaphore:
                return await task()
        
        # Execute tasks in bursts
        for i in range(0, len(tasks), burst_size):
            burst_tasks = tasks[i:i + burst_size]
            
            # Execute burst
            burst_futures = [
                asyncio.create_task(execute_task(task))
                for task in burst_tasks
            ]
            
            burst_results = await asyncio.gather(*burst_futures)
            results.extend(burst_results)
            
            # Wait before next burst (except for the last burst)
            if i + burst_size < len(tasks):
                await asyncio.sleep(burst_interval)
        
        return results
    
    @staticmethod
    async def timeout_wrapper(
        coro: Callable,
        timeout_seconds: float,
        default_result: Any = None
    ) -> Any:
        """Wrap a coroutine with timeout handling.
        
        Args:
            coro: Coroutine to execute
            timeout_seconds: Timeout in seconds
            default_result: Default result if timeout occurs
            
        Returns:
            Coroutine result or default_result if timeout
        """
        try:
            return await asyncio.wait_for(coro, timeout=timeout_seconds)
        except asyncio.TimeoutError:
            return default_result
    
    @staticmethod
    async def retry_with_backoff(
        coro: Callable,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        backoff_factor: float = 2.0
    ) -> Any:
        """Execute coroutine with exponential backoff retry.
        
        Args:
            coro: Coroutine to execute
            max_retries: Maximum number of retries
            base_delay: Base delay in seconds
            max_delay: Maximum delay in seconds
            backoff_factor: Backoff multiplication factor
            
        Returns:
            Coroutine result
            
        Raises:
            Exception: Last exception if all retries fail
        """
        last_exception = None
        delay = base_delay
        
        for attempt in range(max_retries + 1):
            try:
                return await coro()
            except Exception as e:
                last_exception = e
                
                if attempt == max_retries:
                    break
                
                # Wait with exponential backoff
                await asyncio.sleep(min(delay, max_delay))
                delay *= backoff_factor
        
        raise last_exception
    
    @staticmethod
    def create_task_batches(
        tasks: List[Any],
        batch_size: int
    ) -> List[List[Any]]:
        """Create batches of tasks for batch processing.
        
        Args:
            tasks: List of tasks
            batch_size: Size of each batch
            
        Returns:
            List of task batches
        """
        batches = []
        for i in range(0, len(tasks), batch_size):
            batch = tasks[i:i + batch_size]
            batches.append(batch)
        return batches
    
    @staticmethod
    async def monitor_resource_usage(
        duration: float,
        interval: float = 1.0
    ) -> List[dict]:
        """Monitor system resource usage for a duration.
        
        Args:
            duration: Monitoring duration in seconds
            interval: Sampling interval in seconds
            
        Returns:
            List of resource usage samples
        """
        import psutil
        
        samples = []
        start_time = time.time()
        
        while time.time() - start_time < duration:
            sample = {
                'timestamp': time.time(),
                'cpu_percent': psutil.cpu_percent(interval=None),
                'memory_percent': psutil.virtual_memory().percent,
                'memory_used_mb': psutil.virtual_memory().used / (1024 * 1024),
                'disk_io_read_mb': psutil.disk_io_counters().read_bytes / (1024 * 1024),
                'disk_io_write_mb': psutil.disk_io_counters().write_bytes / (1024 * 1024),
            }
            samples.append(sample)
            
            await asyncio.sleep(interval)
        
        return samples
