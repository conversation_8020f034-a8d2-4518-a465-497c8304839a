"""
Synthetic dataset generation for benchmarking.

This module provides functionality for generating synthetic datasets with
configurable parameters for testing classification workflows.
"""

import random
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import numpy as np

from benchmarks.config.benchmark_config import DatasetConfig
from benchmarks.core.dataset_manager import DatasetSample

logger = logging.getLogger(__name__)


@dataclass
class TextTemplate:
    """Template for generating synthetic text."""
    pattern: str
    category: str
    keywords: List[str]
    variations: List[str]


class SyntheticDatasetGenerator:
    """Generates synthetic datasets for benchmarking classification workflows."""
    
    def __init__(self, seed: Optional[int] = None):
        """Initialize the synthetic dataset generator.
        
        Args:
            seed: Random seed for reproducible generation
        """
        self.seed = seed
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)
        
        # Define text templates for different domains
        self._initialize_templates()
    
    def _initialize_templates(self):
        """Initialize text templates for different categories and domains."""
        
        # Insurance domain templates (matching the existing dataset)
        self.insurance_templates = {
            "Billing Inquiries": [
                TextTemplate(
                    pattern="I'm {confused/concerned/puzzled} about a {charge/fee/cost} on my {recent/latest/current} {insurance bill/statement/invoice}",
                    category="Billing Inquiries",
                    keywords=["charge", "bill", "fee", "cost", "payment", "premium", "invoice"],
                    variations=[
                        "I noticed an unexpected charge on my insurance bill",
                        "There's a fee on my statement that I don't recognize",
                        "My premium seems higher than usual this month",
                        "Can you explain this additional cost on my bill?",
                        "I'm seeing a charge that wasn't there before"
                    ]
                )
            ],
            "Policy Administration": [
                TextTemplate(
                    pattern="I'd like to {make changes/modify/adjust} to my {coverage/policy/deductibles}",
                    category="Policy Administration",
                    keywords=["policy", "coverage", "deductible", "modify", "change", "adjust", "update"],
                    variations=[
                        "I want to update my coverage options",
                        "Can I modify my deductible amount?",
                        "I need to make changes to my policy",
                        "How can I adjust my coverage limits?",
                        "I'd like to review my policy options"
                    ]
                )
            ],
            "Claims Assistance": [
                TextTemplate(
                    pattern="I need to {file/submit/report} a claim for {damage/incident/accident}",
                    category="Claims Assistance",
                    keywords=["claim", "damage", "accident", "incident", "file", "submit", "report"],
                    variations=[
                        "I need to file a claim for vehicle damage",
                        "How do I report an accident to insurance?",
                        "I want to submit a claim for property damage",
                        "What's the process for filing a claim?",
                        "I need help with my insurance claim"
                    ]
                )
            ],
            "Coverage Explanations": [
                TextTemplate(
                    pattern="Can you explain the difference between {coverage types/policy options}?",
                    category="Coverage Explanations",
                    keywords=["explain", "difference", "coverage", "comprehensive", "collision", "liability"],
                    variations=[
                        "What's the difference between comprehensive and collision coverage?",
                        "Can you explain what my policy covers?",
                        "I don't understand my coverage options",
                        "What does liability insurance include?",
                        "How does deductible affect my coverage?"
                    ]
                )
            ],
            "Account Management": [
                TextTemplate(
                    pattern="I'm having trouble {accessing/logging into} my {account/online portal}",
                    category="Account Management",
                    keywords=["account", "login", "password", "access", "portal", "website", "online"],
                    variations=[
                        "I can't log into my online account",
                        "My password isn't working for the website",
                        "I'm locked out of my account portal",
                        "How do I reset my account password?",
                        "I need help accessing my online profile"
                    ]
                )
            ]
        }
        
        # General domain templates
        self.general_templates = {
            "positive": [
                TextTemplate(
                    pattern="This is {great/excellent/wonderful/amazing}",
                    category="positive",
                    keywords=["great", "excellent", "wonderful", "amazing", "good", "fantastic"],
                    variations=[
                        "This product is absolutely fantastic",
                        "I'm really happy with this service",
                        "Excellent quality and fast delivery",
                        "Amazing customer support experience",
                        "This exceeded my expectations"
                    ]
                )
            ],
            "negative": [
                TextTemplate(
                    pattern="This is {terrible/awful/disappointing/bad}",
                    category="negative",
                    keywords=["terrible", "awful", "disappointing", "bad", "poor", "horrible"],
                    variations=[
                        "This product is completely disappointing",
                        "Terrible customer service experience",
                        "Poor quality and slow delivery",
                        "I'm very unsatisfied with this",
                        "This did not meet my expectations"
                    ]
                )
            ],
            "neutral": [
                TextTemplate(
                    pattern="This is {okay/average/acceptable}",
                    category="neutral",
                    keywords=["okay", "average", "acceptable", "fine", "decent"],
                    variations=[
                        "This product is just okay",
                        "Average quality for the price",
                        "It's acceptable but nothing special",
                        "Decent service overall",
                        "This meets basic expectations"
                    ]
                )
            ]
        }
    
    def generate_dataset(self, config: DatasetConfig) -> List[DatasetSample]:
        """Generate a synthetic dataset based on configuration.
        
        Args:
            config: Dataset configuration
            
        Returns:
            List of generated dataset samples
        """
        if not config.size:
            raise ValueError("Synthetic datasets must specify a size")
        
        logger.info(f"Generating synthetic dataset '{config.name}' with {config.size} samples")
        
        # Determine class distribution
        distribution = self._get_class_distribution(config)
        
        # Select appropriate templates based on domain
        templates = self._get_templates_for_domain(config.domain, config.categories)
        
        # Generate samples
        samples = []
        for category in config.categories:
            num_samples = int(config.size * distribution[category])
            category_samples = self._generate_category_samples(
                category, num_samples, templates.get(category, []), config
            )
            samples.extend(category_samples)
        
        # Shuffle the samples
        random.shuffle(samples)
        
        # Ensure we have exactly the requested number of samples
        if len(samples) > config.size:
            samples = samples[:config.size]
        elif len(samples) < config.size:
            # Generate additional samples to reach the target
            remaining = config.size - len(samples)
            additional_samples = self._generate_additional_samples(remaining, config, templates)
            samples.extend(additional_samples)
        
        logger.info(f"Generated {len(samples)} synthetic samples")
        return samples
    
    def _get_class_distribution(self, config: DatasetConfig) -> Dict[str, float]:
        """Get the class distribution for sample generation.
        
        Args:
            config: Dataset configuration
            
        Returns:
            Dictionary mapping categories to their proportions
        """
        if config.class_distribution:
            return config.class_distribution
        
        # Default to uniform distribution
        num_categories = len(config.categories)
        return {category: 1.0 / num_categories for category in config.categories}
    
    def _get_templates_for_domain(self, domain: str, categories: List[str]) -> Dict[str, List[TextTemplate]]:
        """Get text templates for the specified domain.
        
        Args:
            domain: Domain name (e.g., 'insurance', 'general')
            categories: List of categories to generate
            
        Returns:
            Dictionary mapping categories to their templates
        """
        if domain == "insurance":
            return {cat: self.insurance_templates.get(cat, []) for cat in categories}
        elif domain == "general":
            return {cat: self.general_templates.get(cat, []) for cat in categories}
        else:
            # For unknown domains, use general templates or create basic ones
            logger.warning(f"Unknown domain '{domain}', using general templates")
            return {cat: self.general_templates.get(cat, []) for cat in categories}
    
    def _generate_category_samples(
        self, 
        category: str, 
        num_samples: int, 
        templates: List[TextTemplate],
        config: DatasetConfig
    ) -> List[DatasetSample]:
        """Generate samples for a specific category.
        
        Args:
            category: Category name
            num_samples: Number of samples to generate
            templates: Text templates for this category
            config: Dataset configuration
            
        Returns:
            List of generated samples
        """
        samples = []
        
        for i in range(num_samples):
            if templates:
                # Use templates if available
                template = random.choice(templates)
                text = self._generate_text_from_template(template, config)
            else:
                # Generate basic text if no templates available
                text = self._generate_basic_text(category, config)
            
            sample = DatasetSample(
                text=text,
                label=category,
                metadata={
                    "generated": True,
                    "domain": config.domain,
                    "generation_method": "template" if templates else "basic"
                }
            )
            samples.append(sample)
        
        return samples
    
    def _generate_text_from_template(self, template: TextTemplate, config: DatasetConfig) -> str:
        """Generate text from a template.
        
        Args:
            template: Text template
            config: Dataset configuration
            
        Returns:
            Generated text
        """
        # Choose from template variations
        if template.variations and random.random() < 0.7:  # 70% chance to use variations
            base_text = random.choice(template.variations)
        else:
            # Use the pattern with random substitutions
            base_text = self._expand_pattern(template.pattern)
        
        # Add some randomness to length
        target_length = random.randint(*config.text_length_range)
        
        # Expand or trim text to target length
        text = self._adjust_text_length(base_text, target_length, template.keywords)
        
        return text
    
    def _expand_pattern(self, pattern: str) -> str:
        """Expand a pattern with random choices.
        
        Args:
            pattern: Pattern string with {option1/option2/option3} syntax
            
        Returns:
            Expanded text
        """
        import re
        
        def replace_choice(match):
            choices = match.group(1).split('/')
            return random.choice(choices)
        
        return re.sub(r'\{([^}]+)\}', replace_choice, pattern)
    
    def _adjust_text_length(self, text: str, target_length: int, keywords: List[str]) -> str:
        """Adjust text length to match target.
        
        Args:
            text: Original text
            target_length: Target length in characters
            keywords: Keywords to use for expansion
            
        Returns:
            Adjusted text
        """
        if len(text) >= target_length:
            return text[:target_length]
        
        # Expand text by adding relevant content
        expansion_phrases = [
            "I would appreciate your help with this matter.",
            "Please let me know what steps I should take.",
            "This is important to me and I need a quick resolution.",
            "I've been a loyal customer for many years.",
            "Thank you for your time and assistance.",
            "I look forward to hearing from you soon.",
        ]
        
        while len(text) < target_length and expansion_phrases:
            phrase = random.choice(expansion_phrases)
            if len(text) + len(phrase) + 1 <= target_length:
                text += " " + phrase
            expansion_phrases.remove(phrase)
        
        return text[:target_length]
    
    def _generate_basic_text(self, category: str, config: DatasetConfig) -> str:
        """Generate basic text when no templates are available.
        
        Args:
            category: Category name
            config: Dataset configuration
            
        Returns:
            Generated text
        """
        basic_phrases = [
            f"This is a sample text for {category} category.",
            f"I need help with {category.lower()} related issues.",
            f"Can you assist me with {category.lower()}?",
            f"I have a question about {category.lower()}.",
            f"Please provide information about {category.lower()}."
        ]
        
        base_text = random.choice(basic_phrases)
        target_length = random.randint(*config.text_length_range)
        
        return self._adjust_text_length(base_text, target_length, [category.lower()])
    
    def _generate_additional_samples(
        self, 
        num_samples: int, 
        config: DatasetConfig, 
        templates: Dict[str, List[TextTemplate]]
    ) -> List[DatasetSample]:
        """Generate additional samples to reach target count.
        
        Args:
            num_samples: Number of additional samples needed
            config: Dataset configuration
            templates: Available templates
            
        Returns:
            List of additional samples
        """
        samples = []
        distribution = self._get_class_distribution(config)
        
        for i in range(num_samples):
            # Choose category based on distribution
            category = np.random.choice(
                list(distribution.keys()),
                p=list(distribution.values())
            )
            
            category_templates = templates.get(category, [])
            category_samples = self._generate_category_samples(
                category, 1, category_templates, config
            )
            samples.extend(category_samples)
        
        return samples
