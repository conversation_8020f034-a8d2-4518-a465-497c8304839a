"""
Metrics collection for benchmarking system.

This module provides functionality for collecting and aggregating performance
metrics during benchmark execution.
"""

import time
import asyncio
import psutil
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict
import threading
from concurrent.futures import Thread<PERSON>oolExecutor

from benchmarks.config.benchmark_config import MetricType, MetricsConfig

logger = logging.getLogger(__name__)


@dataclass
class MetricSample:
    """Represents a single metric measurement."""
    timestamp: float
    metric_type: MetricType
    value: float
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AggregatedMetrics:
    """Aggregated metrics for a benchmark run."""
    metric_type: MetricType
    count: int
    min_value: float
    max_value: float
    mean_value: float
    median_value: float
    p95_value: float
    p99_value: float
    std_dev: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'metric_type': self.metric_type.value,
            'count': self.count,
            'min': self.min_value,
            'max': self.max_value,
            'mean': self.mean_value,
            'median': self.median_value,
            'p95': self.p95_value,
            'p99': self.p99_value,
            'std_dev': self.std_dev
        }


class MetricsCollector:
    """Collects and aggregates performance metrics during benchmark execution."""
    
    def __init__(self, config: MetricsConfig):
        """Initialize the metrics collector.
        
        Args:
            config: Metrics collection configuration
        """
        self.config = config
        self.samples: Dict[MetricType, List[MetricSample]] = defaultdict(list)
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        
        # System resource monitoring
        self._resource_monitor_active = False
        self._resource_monitor_thread: Optional[threading.Thread] = None
        self._resource_samples: List[Dict[str, float]] = []
        
        # Request tracking
        self._request_count = 0
        self._error_count = 0
        self._active_requests = 0
        
    def start_collection(self):
        """Start metrics collection."""
        self.start_time = time.time()
        logger.info("Started metrics collection")
        
        if (self.config.monitor_system_resources and 
            MetricType.MEMORY_USAGE in self.config.enabled_metrics or 
            MetricType.CPU_USAGE in self.config.enabled_metrics):
            self._start_resource_monitoring()
    
    def stop_collection(self):
        """Stop metrics collection."""
        self.end_time = time.time()
        
        if self._resource_monitor_active:
            self._stop_resource_monitoring()
        
        logger.info(f"Stopped metrics collection after {self.get_total_duration():.2f} seconds")
    
    def record_latency(self, latency_seconds: float, metadata: Optional[Dict[str, Any]] = None):
        """Record a latency measurement.
        
        Args:
            latency_seconds: Latency in seconds
            metadata: Optional metadata for this measurement
        """
        if MetricType.LATENCY in self.config.enabled_metrics:
            if self._should_sample():
                sample = MetricSample(
                    timestamp=time.time(),
                    metric_type=MetricType.LATENCY,
                    value=latency_seconds,
                    metadata=metadata or {}
                )
                self.samples[MetricType.LATENCY].append(sample)
    
    def record_accuracy(self, accuracy: float, metadata: Optional[Dict[str, Any]] = None):
        """Record an accuracy measurement.
        
        Args:
            accuracy: Accuracy value (0.0 to 1.0)
            metadata: Optional metadata for this measurement
        """
        if MetricType.ACCURACY in self.config.enabled_metrics:
            if self._should_sample():
                sample = MetricSample(
                    timestamp=time.time(),
                    metric_type=MetricType.ACCURACY,
                    value=accuracy,
                    metadata=metadata or {}
                )
                self.samples[MetricType.ACCURACY].append(sample)
    
    def record_error(self, metadata: Optional[Dict[str, Any]] = None):
        """Record an error occurrence.
        
        Args:
            metadata: Optional metadata for this error
        """
        self._error_count += 1
        
        if MetricType.ERROR_RATE in self.config.enabled_metrics:
            if self._should_sample():
                sample = MetricSample(
                    timestamp=time.time(),
                    metric_type=MetricType.ERROR_RATE,
                    value=1.0,  # Error occurred
                    metadata=metadata or {}
                )
                self.samples[MetricType.ERROR_RATE].append(sample)
    
    def start_request(self) -> str:
        """Mark the start of a request and return a request ID.
        
        Returns:
            Request ID for tracking
        """
        self._request_count += 1
        self._active_requests += 1
        return f"req_{self._request_count}_{time.time()}"
    
    def end_request(self, request_id: str, success: bool = True):
        """Mark the end of a request.
        
        Args:
            request_id: Request ID from start_request
            success: Whether the request was successful
        """
        self._active_requests = max(0, self._active_requests - 1)
        
        if not success:
            self.record_error({"request_id": request_id})
    
    def get_current_throughput(self) -> float:
        """Get current throughput in requests per second.
        
        Returns:
            Current throughput
        """
        if not self.start_time:
            return 0.0
        
        elapsed = time.time() - self.start_time
        if elapsed <= 0:
            return 0.0
        
        return self._request_count / elapsed
    
    def get_current_error_rate(self) -> float:
        """Get current error rate as a percentage.
        
        Returns:
            Error rate (0.0 to 100.0)
        """
        if self._request_count == 0:
            return 0.0
        
        return (self._error_count / self._request_count) * 100.0
    
    def get_total_duration(self) -> float:
        """Get total collection duration in seconds.
        
        Returns:
            Duration in seconds
        """
        if not self.start_time:
            return 0.0
        
        end_time = self.end_time or time.time()
        return end_time - self.start_time
    
    def aggregate_metrics(self) -> Dict[MetricType, AggregatedMetrics]:
        """Aggregate collected metrics.
        
        Returns:
            Dictionary of aggregated metrics by type
        """
        aggregated = {}
        
        for metric_type, samples in self.samples.items():
            if not samples:
                continue
            
            values = [sample.value for sample in samples]
            values.sort()
            
            count = len(values)
            min_val = min(values)
            max_val = max(values)
            mean_val = sum(values) / count
            
            # Calculate percentiles
            median_idx = count // 2
            median_val = values[median_idx] if count % 2 == 1 else (values[median_idx-1] + values[median_idx]) / 2
            
            p95_idx = int(count * 0.95)
            p95_val = values[min(p95_idx, count - 1)]
            
            p99_idx = int(count * 0.99)
            p99_val = values[min(p99_idx, count - 1)]
            
            # Calculate standard deviation
            variance = sum((x - mean_val) ** 2 for x in values) / count
            std_dev = variance ** 0.5
            
            aggregated[metric_type] = AggregatedMetrics(
                metric_type=metric_type,
                count=count,
                min_value=min_val,
                max_value=max_val,
                mean_value=mean_val,
                median_value=median_val,
                p95_value=p95_val,
                p99_value=p99_val,
                std_dev=std_dev
            )
        
        # Add computed metrics
        self._add_computed_metrics(aggregated)
        
        return aggregated
    
    def _add_computed_metrics(self, aggregated: Dict[MetricType, AggregatedMetrics]):
        """Add computed metrics like throughput and error rate.
        
        Args:
            aggregated: Dictionary to add computed metrics to
        """
        duration = self.get_total_duration()
        
        # Add throughput metric
        if MetricType.THROUGHPUT in self.config.enabled_metrics and duration > 0:
            throughput = self._request_count / duration
            aggregated[MetricType.THROUGHPUT] = AggregatedMetrics(
                metric_type=MetricType.THROUGHPUT,
                count=1,
                min_value=throughput,
                max_value=throughput,
                mean_value=throughput,
                median_value=throughput,
                p95_value=throughput,
                p99_value=throughput,
                std_dev=0.0
            )
        
        # Add error rate metric
        if MetricType.ERROR_RATE in self.config.enabled_metrics:
            error_rate = self.get_current_error_rate()
            aggregated[MetricType.ERROR_RATE] = AggregatedMetrics(
                metric_type=MetricType.ERROR_RATE,
                count=1,
                min_value=error_rate,
                max_value=error_rate,
                mean_value=error_rate,
                median_value=error_rate,
                p95_value=error_rate,
                p99_value=error_rate,
                std_dev=0.0
            )
    
    def _should_sample(self) -> bool:
        """Determine if a metric should be sampled based on sample rate.
        
        Returns:
            True if the metric should be sampled
        """
        import random
        return random.random() < self.config.sample_rate
    
    def _start_resource_monitoring(self):
        """Start system resource monitoring in a background thread."""
        self._resource_monitor_active = True
        self._resource_monitor_thread = threading.Thread(
            target=self._resource_monitor_loop,
            daemon=True
        )
        self._resource_monitor_thread.start()
        logger.info("Started system resource monitoring")
    
    def _stop_resource_monitoring(self):
        """Stop system resource monitoring."""
        self._resource_monitor_active = False
        if self._resource_monitor_thread:
            self._resource_monitor_thread.join(timeout=1.0)
        logger.info("Stopped system resource monitoring")
    
    def _resource_monitor_loop(self):
        """Background loop for monitoring system resources."""
        while self._resource_monitor_active:
            try:
                # Collect system metrics
                cpu_percent = psutil.cpu_percent(interval=None)
                memory = psutil.virtual_memory()
                
                timestamp = time.time()
                
                # Record CPU usage
                if MetricType.CPU_USAGE in self.config.enabled_metrics:
                    sample = MetricSample(
                        timestamp=timestamp,
                        metric_type=MetricType.CPU_USAGE,
                        value=cpu_percent,
                        metadata={"unit": "percent"}
                    )
                    self.samples[MetricType.CPU_USAGE].append(sample)
                
                # Record memory usage
                if MetricType.MEMORY_USAGE in self.config.enabled_metrics:
                    memory_percent = memory.percent
                    sample = MetricSample(
                        timestamp=timestamp,
                        metric_type=MetricType.MEMORY_USAGE,
                        value=memory_percent,
                        metadata={
                            "unit": "percent",
                            "total_bytes": memory.total,
                            "used_bytes": memory.used
                        }
                    )
                    self.samples[MetricType.MEMORY_USAGE].append(sample)
                
                time.sleep(self.config.resource_collection_interval)
                
            except Exception as e:
                logger.warning(f"Error in resource monitoring: {e}")
                time.sleep(1.0)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of collected metrics.
        
        Returns:
            Summary dictionary
        """
        aggregated = self.aggregate_metrics()
        
        summary = {
            "collection_duration": self.get_total_duration(),
            "total_requests": self._request_count,
            "total_errors": self._error_count,
            "error_rate_percent": self.get_current_error_rate(),
            "throughput_rps": self.get_current_throughput(),
            "metrics": {
                metric_type.value: metrics.to_dict()
                for metric_type, metrics in aggregated.items()
            }
        }
        
        return summary
