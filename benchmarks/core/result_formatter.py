"""
Result formatting and output for benchmarking system.

This module provides functionality for formatting benchmark results into
various output formats and generating reports.
"""

import json
import csv
import yaml
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import pandas as pd

from benchmarks.config.benchmark_config import OutputFormat, OutputConfig, BenchmarkConfig
from benchmarks.core.metrics_collector import AggregatedMetrics, MetricsCollector

logger = logging.getLogger(__name__)


class ResultFormatter:
    """Formats and outputs benchmark results in various formats."""
    
    def __init__(self, output_config: OutputConfig):
        """Initialize the result formatter.
        
        Args:
            output_config: Output configuration
        """
        self.config = output_config
        self.output_dir = Path(self.config.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def format_and_save_results(
        self,
        benchmark_config: BenchmarkConfig,
        metrics_collector: MetricsCollector,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, str]:
        """Format and save benchmark results.
        
        Args:
            benchmark_config: Benchmark configuration
            metrics_collector: Metrics collector with results
            additional_data: Additional data to include in results
            
        Returns:
            Dictionary mapping output formats to file paths
        """
        # Prepare result data
        result_data = self._prepare_result_data(
            benchmark_config, metrics_collector, additional_data
        )
        
        # Generate timestamp for file naming
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"{benchmark_config.name}_{timestamp}"
        
        output_files = {}
        
        # Save in requested formats
        for output_format in self.config.output_formats:
            try:
                if output_format == OutputFormat.JSON:
                    filepath = self._save_json(result_data, base_filename)
                elif output_format == OutputFormat.CSV:
                    filepath = self._save_csv(result_data, base_filename)
                elif output_format == OutputFormat.YAML:
                    filepath = self._save_yaml(result_data, base_filename)
                else:
                    logger.warning(f"Unsupported output format: {output_format}")
                    continue
                
                output_files[output_format.value] = str(filepath)
                logger.info(f"Saved {output_format.value.upper()} results to: {filepath}")
                
            except Exception as e:
                logger.error(f"Failed to save {output_format.value} results: {e}")
        
        # Generate detailed report if requested
        if self.config.generate_report:
            try:
                report_path = self._generate_report(result_data, base_filename)
                output_files["report"] = str(report_path)
                logger.info(f"Generated detailed report: {report_path}")
            except Exception as e:
                logger.error(f"Failed to generate report: {e}")
        
        return output_files
    
    def _prepare_result_data(
        self,
        benchmark_config: BenchmarkConfig,
        metrics_collector: MetricsCollector,
        additional_data: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Prepare result data for output.
        
        Args:
            benchmark_config: Benchmark configuration
            metrics_collector: Metrics collector
            additional_data: Additional data to include
            
        Returns:
            Formatted result data
        """
        # Get aggregated metrics
        aggregated_metrics = metrics_collector.aggregate_metrics()
        metrics_summary = metrics_collector.get_summary()
        
        # Prepare base result structure
        result_data = {
            "benchmark_info": {
                "name": benchmark_config.name,
                "description": benchmark_config.description,
                "workflow_name": benchmark_config.workflow_name,
                "timestamp": datetime.now().isoformat(),
                "config": benchmark_config.dict()
            },
            "execution_summary": {
                "total_duration_seconds": metrics_summary["collection_duration"],
                "total_requests": metrics_summary["total_requests"],
                "total_errors": metrics_summary["total_errors"],
                "error_rate_percent": metrics_summary["error_rate_percent"],
                "throughput_rps": metrics_summary["throughput_rps"]
            },
            "metrics": {
                metric_type.value: metrics.to_dict()
                for metric_type, metrics in aggregated_metrics.items()
            },
            "dataset_results": []
        }
        
        # Add additional data if provided
        if additional_data:
            result_data.update(additional_data)
        
        # Include raw data if requested
        if self.config.include_raw_data:
            result_data["raw_metrics"] = self._extract_raw_metrics(metrics_collector)
        
        return result_data
    
    def _extract_raw_metrics(self, metrics_collector: MetricsCollector) -> Dict[str, List[Dict[str, Any]]]:
        """Extract raw metrics data.
        
        Args:
            metrics_collector: Metrics collector
            
        Returns:
            Raw metrics data
        """
        raw_data = {}
        
        for metric_type, samples in metrics_collector.samples.items():
            raw_data[metric_type.value] = [
                {
                    "timestamp": sample.timestamp,
                    "value": sample.value,
                    "metadata": sample.metadata
                }
                for sample in samples
            ]
        
        return raw_data
    
    def _save_json(self, data: Dict[str, Any], base_filename: str) -> Path:
        """Save results as JSON.
        
        Args:
            data: Result data
            base_filename: Base filename
            
        Returns:
            Path to saved file
        """
        filepath = self.output_dir / f"{base_filename}.json"
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, default=str)
        
        return filepath
    
    def _save_yaml(self, data: Dict[str, Any], base_filename: str) -> Path:
        """Save results as YAML.
        
        Args:
            data: Result data
            base_filename: Base filename
            
        Returns:
            Path to saved file
        """
        filepath = self.output_dir / f"{base_filename}.yaml"
        
        with open(filepath, 'w', encoding='utf-8') as f:
            yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
        
        return filepath
    
    def _save_csv(self, data: Dict[str, Any], base_filename: str) -> Path:
        """Save results as CSV (flattened metrics).
        
        Args:
            data: Result data
            base_filename: Base filename
            
        Returns:
            Path to saved file
        """
        filepath = self.output_dir / f"{base_filename}.csv"
        
        # Flatten metrics data for CSV format
        rows = []
        
        # Add execution summary
        summary = data["execution_summary"]
        summary_row = {
            "metric_category": "execution_summary",
            "metric_name": "overall",
            **summary
        }
        rows.append(summary_row)
        
        # Add individual metrics
        for metric_name, metric_data in data["metrics"].items():
            row = {
                "metric_category": "performance",
                "metric_name": metric_name,
                **metric_data
            }
            rows.append(row)
        
        # Write CSV
        if rows:
            df = pd.DataFrame(rows)
            df.to_csv(filepath, index=False)
        
        return filepath
    
    def _generate_report(self, data: Dict[str, Any], base_filename: str) -> Path:
        """Generate a detailed HTML report.
        
        Args:
            data: Result data
            base_filename: Base filename
            
        Returns:
            Path to generated report
        """
        filepath = self.output_dir / f"{base_filename}_report.html"
        
        # Generate HTML report
        html_content = self._create_html_report(data)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return filepath
    
    def _create_html_report(self, data: Dict[str, Any]) -> str:
        """Create HTML report content.
        
        Args:
            data: Result data
            
        Returns:
            HTML content
        """
        benchmark_info = data["benchmark_info"]
        execution_summary = data["execution_summary"]
        metrics = data["metrics"]
        
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Benchmark Report: {benchmark_info['name']}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; }}
        .metric-table {{ border-collapse: collapse; width: 100%; }}
        .metric-table th, .metric-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        .metric-table th {{ background-color: #f2f2f2; }}
        .summary-box {{ background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 10px 0; }}
        .error {{ color: #d32f2f; }}
        .success {{ color: #388e3c; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Benchmark Report: {benchmark_info['name']}</h1>
        <p><strong>Workflow:</strong> {benchmark_info['workflow_name']}</p>
        <p><strong>Timestamp:</strong> {benchmark_info['timestamp']}</p>
        {f"<p><strong>Description:</strong> {benchmark_info['description']}</p>" if benchmark_info.get('description') else ""}
    </div>
    
    <div class="section">
        <h2>Execution Summary</h2>
        <div class="summary-box">
            <p><strong>Duration:</strong> {execution_summary['total_duration_seconds']:.2f} seconds</p>
            <p><strong>Total Requests:</strong> {execution_summary['total_requests']}</p>
            <p><strong>Throughput:</strong> {execution_summary['throughput_rps']:.2f} requests/second</p>
            <p class="{'error' if execution_summary['error_rate_percent'] > 0 else 'success'}">
                <strong>Error Rate:</strong> {execution_summary['error_rate_percent']:.2f}%
            </p>
        </div>
    </div>
    
    <div class="section">
        <h2>Performance Metrics</h2>
        <table class="metric-table">
            <thead>
                <tr>
                    <th>Metric</th>
                    <th>Count</th>
                    <th>Mean</th>
                    <th>Median</th>
                    <th>Min</th>
                    <th>Max</th>
                    <th>P95</th>
                    <th>P99</th>
                    <th>Std Dev</th>
                </tr>
            </thead>
            <tbody>
        """
        
        for metric_name, metric_data in metrics.items():
            html += f"""
                <tr>
                    <td>{metric_name}</td>
                    <td>{metric_data['count']}</td>
                    <td>{metric_data['mean']:.4f}</td>
                    <td>{metric_data['median']:.4f}</td>
                    <td>{metric_data['min']:.4f}</td>
                    <td>{metric_data['max']:.4f}</td>
                    <td>{metric_data['p95']:.4f}</td>
                    <td>{metric_data['p99']:.4f}</td>
                    <td>{metric_data['std_dev']:.4f}</td>
                </tr>
            """
        
        html += """
            </tbody>
        </table>
    </div>
    
    <div class="section">
        <h2>Configuration</h2>
        <pre style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;">
        """
        
        # Add configuration details
        config_str = json.dumps(benchmark_info['config'], indent=2, default=str)
        html += config_str
        
        html += """
        </pre>
    </div>
</body>
</html>
        """
        
        return html
    
    def compare_with_baseline(
        self, 
        current_results: Dict[str, Any], 
        baseline_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """Compare current results with baseline.
        
        Args:
            current_results: Current benchmark results
            baseline_path: Path to baseline results file
            
        Returns:
            Comparison results
        """
        if not baseline_path:
            baseline_path = self.config.baseline_path
        
        if not baseline_path or not Path(baseline_path).exists():
            logger.warning("No baseline file found for comparison")
            return {"comparison_available": False}
        
        try:
            # Load baseline results
            with open(baseline_path, 'r') as f:
                if baseline_path.endswith('.json'):
                    baseline_data = json.load(f)
                elif baseline_path.endswith('.yaml') or baseline_path.endswith('.yml'):
                    baseline_data = yaml.safe_load(f)
                else:
                    logger.error(f"Unsupported baseline file format: {baseline_path}")
                    return {"comparison_available": False}
            
            # Compare metrics
            comparison = self._compare_metrics(
                current_results["metrics"],
                baseline_data.get("metrics", {})
            )
            
            comparison["comparison_available"] = True
            comparison["baseline_path"] = baseline_path
            comparison["regression_threshold"] = self.config.regression_threshold
            
            return comparison
            
        except Exception as e:
            logger.error(f"Failed to compare with baseline: {e}")
            return {"comparison_available": False, "error": str(e)}
    
    def _compare_metrics(
        self, 
        current_metrics: Dict[str, Any], 
        baseline_metrics: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Compare current metrics with baseline metrics.
        
        Args:
            current_metrics: Current metrics
            baseline_metrics: Baseline metrics
            
        Returns:
            Comparison results
        """
        comparison = {
            "metric_comparisons": {},
            "regressions_detected": [],
            "improvements_detected": []
        }
        
        threshold = self.config.regression_threshold
        
        for metric_name, current_data in current_metrics.items():
            if metric_name not in baseline_metrics:
                continue
            
            baseline_data = baseline_metrics[metric_name]
            
            # Compare mean values
            current_mean = current_data.get("mean", 0)
            baseline_mean = baseline_data.get("mean", 0)
            
            if baseline_mean > 0:
                change_percent = ((current_mean - baseline_mean) / baseline_mean) * 100
            else:
                change_percent = 0
            
            metric_comparison = {
                "current_mean": current_mean,
                "baseline_mean": baseline_mean,
                "change_percent": change_percent,
                "is_regression": False,
                "is_improvement": False
            }
            
            # Determine if this is a regression or improvement
            # For latency metrics, higher is worse
            if metric_name in ["latency"]:
                if change_percent > threshold * 100:
                    metric_comparison["is_regression"] = True
                    comparison["regressions_detected"].append(metric_name)
                elif change_percent < -threshold * 100:
                    metric_comparison["is_improvement"] = True
                    comparison["improvements_detected"].append(metric_name)
            
            # For throughput and accuracy, higher is better
            elif metric_name in ["throughput", "accuracy"]:
                if change_percent < -threshold * 100:
                    metric_comparison["is_regression"] = True
                    comparison["regressions_detected"].append(metric_name)
                elif change_percent > threshold * 100:
                    metric_comparison["is_improvement"] = True
                    comparison["improvements_detected"].append(metric_name)
            
            comparison["metric_comparisons"][metric_name] = metric_comparison
        
        return comparison
