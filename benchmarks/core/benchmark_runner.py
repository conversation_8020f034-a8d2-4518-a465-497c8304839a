"""
Main benchmark execution engine.

This module provides the core benchmark runner that orchestrates the execution
of benchmarks across different datasets and workflows.
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Callable, Union
from pathlib import Path

from benchmarks.config.benchmark_config import (
    BenchmarkConfig, DatasetType, load_benchmark_config
)
from benchmarks.core.dataset_manager import DatasetManager, DatasetSample
from benchmarks.core.synthetic_generator import SyntheticDatasetGenerator
from benchmarks.core.metrics_collector import MetricsCollector
from benchmarks.core.result_formatter import ResultFormatter

# Import service management for mock setup
from lego_agent.core.di import dependency_registry
from lego_agent.core.services_manager import BaseServiceManager
from tests.mocks.services import (
    MockServiceManager, MockLLMService, MockEmbeddingService, MockVectorDBService
)

logger = logging.getLogger(__name__)


class BenchmarkRunner:
    """Main benchmark execution engine."""
    
    def __init__(self, config: Union[BenchmarkConfig, str, Path]):
        """Initialize the benchmark runner.
        
        Args:
            config: Benchmark configuration or path to config file
        """
        if isinstance(config, (str, Path)):
            self.config = load_benchmark_config(config)
        else:
            self.config = config
        
        # Initialize components
        self.dataset_manager = DatasetManager()
        self.synthetic_generator = SyntheticDatasetGenerator(seed=42)  # Fixed seed for reproducibility
        self.metrics_collector = MetricsCollector(self.config.metrics)
        self.result_formatter = ResultFormatter(self.config.output)
        
        # Workflow function registry
        self.workflow_registry: Dict[str, Callable] = {}
        self.async_workflow_registry: Dict[str, Callable] = {}
        
        # Service setup
        self._original_service_manager = None
        
    def register_workflow(
        self, 
        name: str, 
        sync_func: Optional[Callable] = None,
        async_func: Optional[Callable] = None
    ):
        """Register a workflow for benchmarking.
        
        Args:
            name: Workflow name
            sync_func: Synchronous workflow function
            async_func: Asynchronous workflow function
        """
        if sync_func:
            self.workflow_registry[name] = sync_func
        if async_func:
            self.async_workflow_registry[name] = async_func
        
        logger.info(f"Registered workflow '{name}' (sync: {sync_func is not None}, async: {async_func is not None})")
    
    async def run_benchmark(self) -> Dict[str, Any]:
        """Run the complete benchmark suite.
        
        Returns:
            Benchmark results
        """
        logger.info(f"Starting benchmark: {self.config.name}")
        
        try:
            # Setup services
            await self._setup_services()
            
            # Start metrics collection
            self.metrics_collector.start_collection()
            
            # Run benchmarks for each dataset
            dataset_results = []
            for dataset_config in self.config.datasets:
                logger.info(f"Running benchmark for dataset: {dataset_config.name}")
                
                try:
                    result = await self._run_dataset_benchmark(dataset_config)
                    dataset_results.append(result)
                except Exception as e:
                    logger.error(f"Failed to benchmark dataset {dataset_config.name}: {e}")
                    dataset_results.append({
                        "dataset_name": dataset_config.name,
                        "error": str(e),
                        "success": False
                    })
            
            # Stop metrics collection
            self.metrics_collector.stop_collection()
            
            # Format and save results
            additional_data = {"dataset_results": dataset_results}
            output_files = self.result_formatter.format_and_save_results(
                self.config, self.metrics_collector, additional_data
            )
            
            # Compare with baseline if configured
            comparison_results = None
            if self.config.output.compare_to_baseline:
                # Load the latest results for comparison
                latest_results = self._get_latest_results(output_files)
                if latest_results:
                    comparison_results = self.result_formatter.compare_with_baseline(latest_results)
            
            logger.info(f"Benchmark completed: {self.config.name}")
            
            return {
                "benchmark_name": self.config.name,
                "success": True,
                "output_files": output_files,
                "dataset_results": dataset_results,
                "metrics_summary": self.metrics_collector.get_summary(),
                "comparison_results": comparison_results
            }
            
        except Exception as e:
            logger.error(f"Benchmark failed: {e}")
            return {
                "benchmark_name": self.config.name,
                "success": False,
                "error": str(e)
            }
        finally:
            # Cleanup services
            await self._cleanup_services()
    
    async def _run_dataset_benchmark(self, dataset_config) -> Dict[str, Any]:
        """Run benchmark for a specific dataset.
        
        Args:
            dataset_config: Dataset configuration
            
        Returns:
            Dataset benchmark results
        """
        start_time = time.time()
        
        try:
            # Load or generate dataset
            if dataset_config.type == DatasetType.REAL:
                samples = self.dataset_manager.load_dataset(dataset_config)
            else:  # SYNTHETIC
                samples = self.synthetic_generator.generate_dataset(dataset_config)
            
            logger.info(f"Loaded {len(samples)} samples for dataset {dataset_config.name}")
            
            # Run workflow benchmarks
            results = await self._execute_workflow_on_dataset(samples, dataset_config)
            
            end_time = time.time()
            
            return {
                "dataset_name": dataset_config.name,
                "dataset_type": dataset_config.type.value,
                "sample_count": len(samples),
                "execution_time": end_time - start_time,
                "success": True,
                **results
            }
            
        except Exception as e:
            logger.error(f"Dataset benchmark failed for {dataset_config.name}: {e}")
            return {
                "dataset_name": dataset_config.name,
                "error": str(e),
                "success": False
            }
    
    async def _execute_workflow_on_dataset(
        self, 
        samples: List[DatasetSample], 
        dataset_config
    ) -> Dict[str, Any]:
        """Execute workflow on dataset samples.
        
        Args:
            samples: Dataset samples
            dataset_config: Dataset configuration
            
        Returns:
            Execution results
        """
        workflow_name = self.config.workflow_name
        
        # Get workflow function
        if workflow_name in self.async_workflow_registry:
            workflow_func = self.async_workflow_registry[workflow_name]
            use_async = True
        elif workflow_name in self.workflow_registry:
            workflow_func = self.workflow_registry[workflow_name]
            use_async = False
        else:
            raise ValueError(f"Workflow '{workflow_name}' not registered")
        
        # Execute based on configuration
        execution_config = self.config.execution
        
        if execution_config.concurrency_level > 1 and use_async:
            results = await self._execute_concurrent(
                workflow_func, samples, dataset_config, execution_config
            )
        else:
            results = await self._execute_sequential(
                workflow_func, samples, dataset_config, execution_config, use_async
            )
        
        return results
    
    async def _execute_sequential(
        self,
        workflow_func: Callable,
        samples: List[DatasetSample],
        dataset_config,
        execution_config,
        use_async: bool
    ) -> Dict[str, Any]:
        """Execute workflow sequentially.
        
        Args:
            workflow_func: Workflow function
            samples: Dataset samples
            dataset_config: Dataset configuration
            execution_config: Execution configuration
            use_async: Whether to use async execution
            
        Returns:
            Execution results
        """
        correct_predictions = 0
        total_predictions = 0
        errors = []
        
        # Limit samples if max_requests is specified
        if execution_config.max_requests:
            samples = samples[:execution_config.max_requests]
        
        # Warmup requests
        warmup_samples = samples[:execution_config.warmup_requests]
        for sample in warmup_samples:
            try:
                await self._execute_single_request(
                    workflow_func, sample, dataset_config, use_async, record_metrics=False
                )
            except Exception as e:
                logger.warning(f"Warmup request failed: {e}")
        
        # Main execution
        main_samples = samples[execution_config.warmup_requests:]
        
        for i, sample in enumerate(main_samples):
            try:
                request_id = self.metrics_collector.start_request()
                
                start_time = time.time()
                prediction = await self._execute_single_request(
                    workflow_func, sample, dataset_config, use_async, record_metrics=True
                )
                end_time = time.time()
                
                # Record latency
                latency = end_time - start_time
                self.metrics_collector.record_latency(latency, {"sample_index": i})
                
                # Check accuracy
                is_correct = prediction.lower() == sample.label.lower()
                if is_correct:
                    correct_predictions += 1
                total_predictions += 1
                
                self.metrics_collector.end_request(request_id, success=True)
                
                # Add delay if specified in request pattern
                if execution_config.request_rate:
                    delay = 1.0 / execution_config.request_rate
                    await asyncio.sleep(delay)
                
            except Exception as e:
                logger.error(f"Request failed for sample {i}: {e}")
                errors.append(str(e))
                self.metrics_collector.record_error({"sample_index": i, "error": str(e)})
                self.metrics_collector.end_request(request_id, success=False)
        
        # Calculate accuracy
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.0
        self.metrics_collector.record_accuracy(accuracy)
        
        return {
            "total_samples": len(main_samples),
            "correct_predictions": correct_predictions,
            "accuracy": accuracy,
            "error_count": len(errors),
            "errors": errors[:10]  # Limit error list
        }
    
    async def _execute_concurrent(
        self,
        workflow_func: Callable,
        samples: List[DatasetSample],
        dataset_config,
        execution_config
    ) -> Dict[str, Any]:
        """Execute workflow with concurrency.
        
        Args:
            workflow_func: Async workflow function
            samples: Dataset samples
            dataset_config: Dataset configuration
            execution_config: Execution configuration
            
        Returns:
            Execution results
        """
        # Limit samples if max_requests is specified
        if execution_config.max_requests:
            samples = samples[:execution_config.max_requests]
        
        # Skip warmup samples for concurrent execution
        main_samples = samples[execution_config.warmup_requests:]
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(execution_config.concurrency_level)
        
        async def execute_with_semaphore(sample, index):
            async with semaphore:
                try:
                    request_id = self.metrics_collector.start_request()
                    
                    start_time = time.time()
                    prediction = await self._execute_single_request(
                        workflow_func, sample, dataset_config, True, record_metrics=True
                    )
                    end_time = time.time()
                    
                    # Record latency
                    latency = end_time - start_time
                    self.metrics_collector.record_latency(latency, {"sample_index": index})
                    
                    # Check accuracy
                    is_correct = prediction.lower() == sample.label.lower()
                    
                    self.metrics_collector.end_request(request_id, success=True)
                    
                    return {
                        "success": True,
                        "prediction": prediction,
                        "correct": is_correct,
                        "latency": latency
                    }
                    
                except Exception as e:
                    logger.error(f"Concurrent request failed for sample {index}: {e}")
                    self.metrics_collector.record_error({"sample_index": index, "error": str(e)})
                    self.metrics_collector.end_request(request_id, success=False)
                    
                    return {
                        "success": False,
                        "error": str(e)
                    }
        
        # Execute all requests concurrently
        tasks = [
            execute_with_semaphore(sample, i) 
            for i, sample in enumerate(main_samples)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Aggregate results
        correct_predictions = 0
        total_predictions = 0
        errors = []
        
        for result in results:
            if isinstance(result, dict) and result.get("success"):
                total_predictions += 1
                if result.get("correct"):
                    correct_predictions += 1
            elif isinstance(result, dict):
                errors.append(result.get("error", "Unknown error"))
            else:
                errors.append(str(result))
        
        # Calculate accuracy
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.0
        self.metrics_collector.record_accuracy(accuracy)
        
        return {
            "total_samples": len(main_samples),
            "correct_predictions": correct_predictions,
            "accuracy": accuracy,
            "error_count": len(errors),
            "errors": errors[:10]  # Limit error list
        }
    
    async def _execute_single_request(
        self,
        workflow_func: Callable,
        sample: DatasetSample,
        dataset_config,
        use_async: bool,
        record_metrics: bool = True
    ) -> str:
        """Execute a single workflow request.

        Args:
            workflow_func: Workflow function
            sample: Dataset sample
            dataset_config: Dataset configuration
            use_async: Whether to use async execution
            record_metrics: Whether to record metrics

        Returns:
            Prediction result
        """
        try:
            if use_async:
                prediction = await workflow_func(sample, dataset_config)
            else:
                prediction = workflow_func(sample, dataset_config)

            return prediction

        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")
            # Return a default category in case of error
            return dataset_config.categories[0] if dataset_config.categories else "Unknown"
    
    async def _setup_services(self):
        """Setup mock services for benchmarking."""
        if not self.config.execution.use_mock_services:
            logger.info("Using real services for benchmarking")
            return
        
        logger.info("Setting up mock services for benchmarking")
        
        # Store original service manager
        try:
            self._original_service_manager = dependency_registry.resolve(BaseServiceManager)
        except ValueError:
            self._original_service_manager = None
        
        # Create mock service manager
        mock_manager = MockServiceManager()
        
        # Add mock services with configured delays
        delay = self.config.execution.mock_service_delay
        
        mock_llm = MockLLMService(
            responses=["Mock classification response"], 
            delay=delay
        )
        mock_embedding = MockEmbeddingService(
            embedding_dim=384, 
            delay=delay
        )
        mock_vdb = MockVectorDBService(delay=delay)
        
        mock_manager.add_mock_service("llm", mock_llm)
        mock_manager.add_mock_service("embedding", mock_embedding)
        mock_manager.add_mock_service("vdb", mock_vdb)
        
        # Register mock service manager
        dependency_registry.register(BaseServiceManager, mock_manager)
        
        logger.info("Mock services setup complete")
    
    async def _cleanup_services(self):
        """Cleanup services after benchmarking."""
        if self._original_service_manager:
            dependency_registry.register(BaseServiceManager, self._original_service_manager)
            logger.info("Restored original service manager")
    
    def _get_latest_results(self, output_files: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """Get the latest results for comparison.
        
        Args:
            output_files: Dictionary of output files
            
        Returns:
            Latest results data or None
        """
        json_file = output_files.get("json")
        if not json_file or not Path(json_file).exists():
            return None
        
        try:
            import json
            with open(json_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load results for comparison: {e}")
            return None
