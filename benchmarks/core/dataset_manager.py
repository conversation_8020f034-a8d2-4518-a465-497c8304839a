"""
Dataset management for benchmarking system.

This module provides functionality for loading, validating, and managing
datasets used in benchmarking workflows.
"""

import csv
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Iterator, Tuple
import pandas as pd
from dataclasses import dataclass

from benchmarks.config.benchmark_config import DatasetConfig, DatasetType

logger = logging.getLogger(__name__)


@dataclass
class DatasetSample:
    """Represents a single sample from a dataset."""
    text: str
    label: str
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class DatasetManager:
    """Manages dataset loading, validation, and access for benchmarking."""
    
    def __init__(self, base_path: Optional[str] = None):
        """Initialize the dataset manager.
        
        Args:
            base_path: Base directory path for dataset files
        """
        self.base_path = Path(base_path) if base_path else Path("benchmarks/datasets")
        self.loaded_datasets: Dict[str, List[DatasetSample]] = {}
        
    def load_dataset(self, config: DatasetConfig) -> List[DatasetSample]:
        """Load a dataset based on its configuration.
        
        Args:
            config: Dataset configuration
            
        Returns:
            List of dataset samples
            
        Raises:
            FileNotFoundError: If dataset file is not found
            ValueError: If dataset format is invalid
        """
        if config.name in self.loaded_datasets:
            logger.info(f"Dataset '{config.name}' already loaded, returning cached version")
            return self.loaded_datasets[config.name]
        
        if config.type == DatasetType.REAL:
            samples = self._load_real_dataset(config)
        elif config.type == DatasetType.SYNTHETIC:
            # For synthetic datasets, we'll generate them using the SyntheticDatasetGenerator
            # This is a placeholder - actual generation happens in the synthetic generator
            raise NotImplementedError("Synthetic dataset loading should use SyntheticDatasetGenerator")
        else:
            raise ValueError(f"Unsupported dataset type: {config.type}")
        
        # Validate the loaded dataset
        self._validate_dataset(samples, config)
        
        # Cache the dataset
        self.loaded_datasets[config.name] = samples
        
        logger.info(f"Loaded dataset '{config.name}' with {len(samples)} samples")
        return samples
    
    def _load_real_dataset(self, config: DatasetConfig) -> List[DatasetSample]:
        """Load a real dataset from file.
        
        Args:
            config: Dataset configuration
            
        Returns:
            List of dataset samples
        """
        if not config.path:
            raise ValueError("Real datasets must specify a path")
        
        dataset_path = self.base_path / config.path
        if not dataset_path.exists():
            # Try absolute path
            dataset_path = Path(config.path)
            if not dataset_path.exists():
                raise FileNotFoundError(f"Dataset file not found: {config.path}")
        
        logger.info(f"Loading real dataset from: {dataset_path}")
        
        # Determine file format and load accordingly
        if dataset_path.suffix.lower() == '.csv':
            return self._load_csv_dataset(dataset_path, config)
        elif dataset_path.suffix.lower() == '.json':
            return self._load_json_dataset(dataset_path, config)
        else:
            raise ValueError(f"Unsupported dataset format: {dataset_path.suffix}")
    
    def _load_csv_dataset(self, path: Path, config: DatasetConfig) -> List[DatasetSample]:
        """Load dataset from CSV file.
        
        Expected CSV format:
        - 'text' column: The text to classify
        - 'label' column: The classification label
        - Additional columns become metadata
        
        Args:
            path: Path to CSV file
            config: Dataset configuration
            
        Returns:
            List of dataset samples
        """
        samples = []
        
        try:
            df = pd.read_csv(path)
        except Exception as e:
            raise ValueError(f"Failed to read CSV file {path}: {e}")
        
        # Validate required columns
        required_columns = ['text', 'label']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"CSV file missing required columns: {missing_columns}")
        
        # Convert to DatasetSample objects
        for _, row in df.iterrows():
            # Extract metadata (all columns except text and label)
            metadata = {
                col: row[col] for col in df.columns 
                if col not in ['text', 'label'] and pd.notna(row[col])
            }
            
            sample = DatasetSample(
                text=str(row['text']),
                label=str(row['label']),
                metadata=metadata
            )
            samples.append(sample)
        
        return samples
    
    def _load_json_dataset(self, path: Path, config: DatasetConfig) -> List[DatasetSample]:
        """Load dataset from JSON file.
        
        Expected JSON format:
        [
            {
                "text": "sample text",
                "label": "category",
                "metadata": {...}  // optional
            },
            ...
        ]
        
        Args:
            path: Path to JSON file
            config: Dataset configuration
            
        Returns:
            List of dataset samples
        """
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            raise ValueError(f"Failed to read JSON file {path}: {e}")
        
        if not isinstance(data, list):
            raise ValueError("JSON dataset must be a list of objects")
        
        samples = []
        for i, item in enumerate(data):
            if not isinstance(item, dict):
                raise ValueError(f"Item {i} in JSON dataset must be an object")
            
            if 'text' not in item or 'label' not in item:
                raise ValueError(f"Item {i} missing required 'text' or 'label' field")
            
            metadata = item.get('metadata', {})
            # Include any additional fields as metadata
            for key, value in item.items():
                if key not in ['text', 'label', 'metadata']:
                    metadata[key] = value
            
            sample = DatasetSample(
                text=str(item['text']),
                label=str(item['label']),
                metadata=metadata
            )
            samples.append(sample)
        
        return samples
    
    def _validate_dataset(self, samples: List[DatasetSample], config: DatasetConfig):
        """Validate that the loaded dataset matches the configuration.
        
        Args:
            samples: List of dataset samples
            config: Dataset configuration
            
        Raises:
            ValueError: If validation fails
        """
        if not samples:
            raise ValueError("Dataset is empty")
        
        # Check that all labels are in the configured categories
        dataset_labels = set(sample.label for sample in samples)
        config_categories = set(config.categories)
        
        unknown_labels = dataset_labels - config_categories
        if unknown_labels:
            logger.warning(f"Dataset contains labels not in config categories: {unknown_labels}")
        
        missing_labels = config_categories - dataset_labels
        if missing_labels:
            logger.warning(f"Config categories not found in dataset: {missing_labels}")
        
        # Log dataset statistics
        label_counts = {}
        for sample in samples:
            label_counts[sample.label] = label_counts.get(sample.label, 0) + 1
        
        logger.info(f"Dataset statistics for '{config.name}':")
        logger.info(f"  Total samples: {len(samples)}")
        logger.info(f"  Unique labels: {len(dataset_labels)}")
        for label, count in sorted(label_counts.items()):
            logger.info(f"    {label}: {count} samples ({count/len(samples)*100:.1f}%)")
    
    def get_dataset_iterator(self, config: DatasetConfig, batch_size: int = 1) -> Iterator[List[DatasetSample]]:
        """Get an iterator over dataset batches.
        
        Args:
            config: Dataset configuration
            batch_size: Size of each batch
            
        Yields:
            Batches of dataset samples
        """
        samples = self.load_dataset(config)
        
        for i in range(0, len(samples), batch_size):
            yield samples[i:i + batch_size]
    
    def get_dataset_stats(self, config: DatasetConfig) -> Dict[str, Any]:
        """Get statistics about a dataset.
        
        Args:
            config: Dataset configuration
            
        Returns:
            Dictionary containing dataset statistics
        """
        samples = self.load_dataset(config)
        
        label_counts = {}
        text_lengths = []
        
        for sample in samples:
            label_counts[sample.label] = label_counts.get(sample.label, 0) + 1
            text_lengths.append(len(sample.text))
        
        return {
            'total_samples': len(samples),
            'unique_labels': len(label_counts),
            'label_distribution': label_counts,
            'text_length_stats': {
                'min': min(text_lengths) if text_lengths else 0,
                'max': max(text_lengths) if text_lengths else 0,
                'mean': sum(text_lengths) / len(text_lengths) if text_lengths else 0,
            }
        }
    
    def clear_cache(self):
        """Clear the dataset cache."""
        self.loaded_datasets.clear()
        logger.info("Dataset cache cleared")
