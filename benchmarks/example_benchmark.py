#!/usr/bin/env python3
"""
Example benchmark script for classification workflows.

This script demonstrates how to use the benchmarking system to evaluate
classification workflow performance with different datasets and configurations.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from benchmarks.workflows.classification_benchmark import (
    ClassificationBenchmark,
    create_classification_benchmark_config,
    run_classification_benchmark
)
from benchmarks.config.benchmark_config import load_benchmark_config


# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def run_small_benchmark():
    """Run a small-scale benchmark for quick testing."""
    logger.info("Running small-scale classification benchmark...")
    
    # Create configuration
    config = create_classification_benchmark_config(
        name="quick_test_benchmark",
        dataset_configs=[
            {
                "name": "real_insurance_sample",
                "type": "real",
                "path": "benchmarks/datasets/real/insurance_classification_dataset.csv",
                "categories": [
                    "Billing Inquiries",
                    "Policy Administration",
                    "Claims Assistance",
                    "Coverage Explanations",
                    "Account Management"
                ]
            },
            {
                "name": "synthetic_test",
                "type": "synthetic",
                "size": 20,
                "categories": [
                    "Billing Inquiries",
                    "Claims Assistance",
                    "Coverage Explanations"
                ],
                "domain": "insurance",
                "text_length_range": [50, 200]
            }
        ],
        execution_params={
            "max_requests": 10,
            "concurrency_level": 1,
            "warmup_requests": 2,
            "use_mock_services": True,
            "mock_service_delay": 0.01,
        }
    )
    
    # Run benchmark
    results = await run_classification_benchmark(config, setup_data=True)
    
    logger.info(f"Small benchmark completed. Success: {results['success']}")
    if results['success']:
        logger.info(f"Output files: {results['output_files']}")
        logger.info(f"Metrics summary: {results['metrics_summary']}")
    
    return results


async def run_config_file_benchmark():
    """Run benchmark using a configuration file."""
    logger.info("Running benchmark from configuration file...")
    
    config_path = Path("benchmarks/config/configs/classification_workflow_small.yaml")
    
    if not config_path.exists():
        logger.error(f"Configuration file not found: {config_path}")
        return None
    
    # Load configuration
    config = load_benchmark_config(config_path)
    
    # Update dataset path to use our copied dataset
    for dataset in config.datasets:
        if dataset.type.value == "real":
            dataset.path = "benchmarks/datasets/real/insurance_classification_dataset.csv"
    
    # Run benchmark
    benchmark = ClassificationBenchmark(config)
    results = await benchmark.run_benchmark()
    
    logger.info(f"Config file benchmark completed. Success: {results['success']}")
    if results['success']:
        logger.info(f"Output files: {results['output_files']}")
    
    return results


async def run_synthetic_dataset_demo():
    """Demonstrate synthetic dataset generation."""
    logger.info("Running synthetic dataset generation demo...")
    
    config = create_classification_benchmark_config(
        name="synthetic_demo",
        dataset_configs=[
            {
                "name": "balanced_synthetic",
                "type": "synthetic",
                "size": 50,
                "categories": [
                    "Billing Inquiries",
                    "Policy Administration",
                    "Claims Assistance"
                ],
                "domain": "insurance",
                "text_length_range": [100, 300],
                "class_distribution": {
                    "Billing Inquiries": 0.5,
                    "Policy Administration": 0.3,
                    "Claims Assistance": 0.2
                }
            },
            {
                "name": "edge_case_synthetic",
                "type": "synthetic",
                "size": 30,
                "categories": [
                    "Billing Inquiries",
                    "Claims Assistance"
                ],
                "domain": "insurance",
                "text_length_range": [10, 800],  # Wide range for edge cases
            }
        ],
        execution_params={
            "max_requests": 25,
            "concurrency_level": 2,
            "use_mock_services": True,
            "mock_service_delay": 0.02,
        }
    )
    
    results = await run_classification_benchmark(config)
    
    logger.info(f"Synthetic demo completed. Success: {results['success']}")
    return results


async def run_performance_comparison():
    """Run performance comparison between sync and async execution."""
    logger.info("Running performance comparison...")
    
    # Test with different concurrency levels
    concurrency_levels = [1, 2, 4]
    results = {}
    
    for concurrency in concurrency_levels:
        logger.info(f"Testing with concurrency level: {concurrency}")
        
        config = create_classification_benchmark_config(
            name=f"perf_test_concurrency_{concurrency}",
            dataset_configs=[
                {
                    "name": "perf_test_dataset",
                    "type": "synthetic",
                    "size": 40,
                    "categories": [
                        "Billing Inquiries",
                        "Claims Assistance",
                        "Coverage Explanations"
                    ],
                    "domain": "insurance",
                    "text_length_range": [100, 200]
                }
            ],
            execution_params={
                "max_requests": 20,
                "concurrency_level": concurrency,
                "warmup_requests": 3,
                "use_mock_services": True,
                "mock_service_delay": 0.05,
            }
        )
        
        result = await run_classification_benchmark(config)
        if result['success']:
            metrics = result['metrics_summary']
            results[concurrency] = {
                'throughput': metrics['throughput_rps'],
                'duration': metrics['collection_duration'],
                'error_rate': metrics['error_rate_percent']
            }
    
    # Print comparison
    logger.info("Performance Comparison Results:")
    logger.info("Concurrency | Throughput (RPS) | Duration (s) | Error Rate (%)")
    logger.info("-" * 65)
    for concurrency, metrics in results.items():
        logger.info(f"{concurrency:10d} | {metrics['throughput']:15.2f} | {metrics['duration']:11.2f} | {metrics['error_rate']:12.2f}")
    
    return results


async def main():
    """Main function to run example benchmarks."""
    logger.info("Starting benchmark examples...")
    
    try:
        # Run different types of benchmarks
        logger.info("=" * 60)
        await run_small_benchmark()
        
        logger.info("=" * 60)
        await run_config_file_benchmark()
        
        logger.info("=" * 60)
        await run_synthetic_dataset_demo()
        
        logger.info("=" * 60)
        await run_performance_comparison()
        
        logger.info("=" * 60)
        logger.info("All benchmark examples completed successfully!")
        
    except Exception as e:
        logger.error(f"Benchmark examples failed: {e}")
        raise


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
