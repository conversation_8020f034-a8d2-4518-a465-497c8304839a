"""
Classification workflow benchmarking implementation.

This module provides specialized benchmarking for classification workflows,
including the RAG Chain of Thought classification workflow.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

from benchmarks.core.benchmark_runner import BenchmarkRunner
from benchmarks.core.dataset_manager import DatasetSample
from benchmarks.config.benchmark_config import BenchmarkConfig

# Import classification workflow components
from lego_agent.agents.anthropic.skills.classification.workflow.rag_chain_of_thought import (
    RAGChainOfThoughtWorkflowRequest,
    rag_chain_of_thought_classify,
    rag_chain_of_thought_classify_async,
)

logger = logging.getLogger(__name__)


class ClassificationBenchmark(BenchmarkRunner):
    """Specialized benchmark runner for classification workflows."""
    
    def __init__(self, config: BenchmarkConfig):
        """Initialize the classification benchmark runner.
        
        Args:
            config: Benchmark configuration
        """
        super().__init__(config)
        
        # Register classification workflows
        self._register_classification_workflows()
    
    def _register_classification_workflows(self):
        """Register available classification workflows."""
        # Register RAG Chain of Thought workflow
        self.register_workflow(
            "rag_chain_of_thought",
            sync_func=self._rag_chain_of_thought_sync,
            async_func=self._rag_chain_of_thought_async
        )
        
        logger.info("Registered classification workflows")
    
    def _rag_chain_of_thought_sync(
        self, 
        sample: DatasetSample, 
        dataset_config, 
        **kwargs
    ) -> str:
        """Execute RAG Chain of Thought workflow synchronously.
        
        Args:
            sample: Dataset sample
            dataset_config: Dataset configuration
            **kwargs: Additional parameters
            
        Returns:
            Classification prediction
        """
        # Create workflow request
        request = self._create_workflow_request(sample, dataset_config)
        
        # Execute workflow
        response = rag_chain_of_thought_classify(request)
        
        return response.category
    
    async def _rag_chain_of_thought_async(
        self, 
        sample: DatasetSample, 
        dataset_config, 
        **kwargs
    ) -> str:
        """Execute RAG Chain of Thought workflow asynchronously.
        
        Args:
            sample: Dataset sample
            dataset_config: Dataset configuration
            **kwargs: Additional parameters
            
        Returns:
            Classification prediction
        """
        # Create workflow request
        request = self._create_workflow_request(sample, dataset_config)
        
        # Execute workflow
        response = await rag_chain_of_thought_classify_async(request)
        
        return response.category
    
    def _create_workflow_request(
        self, 
        sample: DatasetSample, 
        dataset_config
    ) -> RAGChainOfThoughtWorkflowRequest:
        """Create a workflow request from a dataset sample.
        
        Args:
            sample: Dataset sample
            dataset_config: Dataset configuration
            
        Returns:
            Workflow request
        """
        # Get workflow parameters from config
        workflow_params = self.config.workflow_params
        
        request = RAGChainOfThoughtWorkflowRequest(
            text=sample.text,
            categories=dataset_config.categories,
            collection_name=workflow_params.get("collection_name", "simple"),
            num_examples=workflow_params.get("num_examples", 5),
            llm_model=workflow_params.get("llm_model"),
            embedding_model=workflow_params.get("embedding_model"),
            max_tokens=workflow_params.get("max_tokens", 4096),
            temperature=workflow_params.get("temperature", 0.0),
        )
        
        return request
    
    async def _execute_single_request(
        self,
        workflow_func,
        sample: DatasetSample,
        dataset_config,
        use_async: bool,
        record_metrics: bool = True
    ) -> str:
        """Execute a single classification request.
        
        Args:
            workflow_func: Workflow function
            sample: Dataset sample
            dataset_config: Dataset configuration
            use_async: Whether to use async execution
            record_metrics: Whether to record metrics
            
        Returns:
            Classification prediction
        """
        try:
            if use_async:
                prediction = await workflow_func(sample, dataset_config)
            else:
                prediction = workflow_func(sample, dataset_config)
            
            return prediction
            
        except Exception as e:
            logger.error(f"Classification request failed: {e}")
            # Return a default category in case of error
            return dataset_config.categories[0] if dataset_config.categories else "Unknown"


def create_classification_benchmark_config(
    name: str,
    dataset_configs: List[Dict[str, Any]],
    workflow_params: Optional[Dict[str, Any]] = None,
    execution_params: Optional[Dict[str, Any]] = None,
    output_dir: str = "benchmarks/results"
) -> BenchmarkConfig:
    """Create a benchmark configuration for classification workflows.
    
    Args:
        name: Benchmark name
        dataset_configs: List of dataset configurations
        workflow_params: Workflow-specific parameters
        execution_params: Execution parameters
        output_dir: Output directory for results
        
    Returns:
        Benchmark configuration
    """
    from benchmarks.config.benchmark_config import (
        BenchmarkConfig, DatasetConfig, ExecutionConfig, 
        MetricsConfig, OutputConfig, DatasetType, MetricType
    )
    
    # Convert dataset configs
    datasets = []
    for dataset_config in dataset_configs:
        datasets.append(DatasetConfig(**dataset_config))
    
    # Default workflow parameters for classification
    default_workflow_params = {
        "collection_name": "simple",
        "num_examples": 5,
        "max_tokens": 4096,
        "temperature": 0.0,
    }
    if workflow_params:
        default_workflow_params.update(workflow_params)
    
    # Default execution parameters
    default_execution_params = {
        "batch_size": 1,
        "concurrency_level": 1,
        "timeout_seconds": 300.0,
        "warmup_requests": 5,
        "use_mock_services": True,
        "mock_service_delay": 0.1,
    }
    if execution_params:
        default_execution_params.update(execution_params)
    
    # Create configuration
    config = BenchmarkConfig(
        name=name,
        description=f"Classification workflow benchmark: {name}",
        workflow_name="rag_chain_of_thought",
        datasets=datasets,
        execution=ExecutionConfig(**default_execution_params),
        metrics=MetricsConfig(
            enabled_metrics=[
                MetricType.LATENCY,
                MetricType.THROUGHPUT,
                MetricType.ACCURACY,
                MetricType.ERROR_RATE,
                MetricType.MEMORY_USAGE,
                MetricType.CPU_USAGE,
            ]
        ),
        output=OutputConfig(
            output_dir=output_dir,
            generate_report=True,
            compare_to_baseline=True,
        ),
        workflow_params=default_workflow_params,
    )
    
    return config


async def run_classification_benchmark(
    config: BenchmarkConfig,
    setup_data: bool = True
) -> Dict[str, Any]:
    """Run a classification benchmark.
    
    Args:
        config: Benchmark configuration
        setup_data: Whether to setup test data
        
    Returns:
        Benchmark results
    """
    # Setup test data if requested
    if setup_data:
        await setup_classification_test_data()
    
    # Create and run benchmark
    benchmark = ClassificationBenchmark(config)
    results = await benchmark.run_benchmark()
    
    return results


async def setup_classification_test_data():
    """Setup test data for classification benchmarks."""
    # This function would setup vector database with example data
    # For now, we'll just log that setup is happening
    logger.info("Setting up classification test data...")
    
    # In a real implementation, this would:
    # 1. Load example classification data
    # 2. Generate embeddings
    # 3. Store in vector database
    # 4. Ensure the collection is ready for retrieval
    
    # Simulate setup time
    await asyncio.sleep(0.5)
    logger.info("Classification test data setup complete")


# Example usage function
def create_example_classification_benchmark() -> BenchmarkConfig:
    """Create an example classification benchmark configuration.
    
    Returns:
        Example benchmark configuration
    """
    # Define datasets
    dataset_configs = [
        {
            "name": "small_insurance_dataset",
            "type": "real",
            "path": ".cache/data/classification_workflow/dataset.csv",
            "categories": [
                "Billing Inquiries",
                "Policy Administration", 
                "Claims Assistance",
                "Coverage Explanations",
                "Quotes and Proposals",
                "Account Management",
                "Billing Disputes",
                "Claims Disputes",
                "Policy Comparisons",
                "General Inquiries"
            ]
        },
        {
            "name": "synthetic_insurance_small",
            "type": "synthetic",
            "size": 100,
            "categories": [
                "Billing Inquiries",
                "Policy Administration",
                "Claims Assistance",
                "Coverage Explanations",
                "Account Management"
            ],
            "domain": "insurance",
            "text_length_range": [50, 300]
        },
        {
            "name": "synthetic_insurance_medium",
            "type": "synthetic", 
            "size": 500,
            "categories": [
                "Billing Inquiries",
                "Policy Administration",
                "Claims Assistance", 
                "Coverage Explanations",
                "Quotes and Proposals",
                "Account Management"
            ],
            "domain": "insurance",
            "text_length_range": [100, 500]
        }
    ]
    
    # Create configuration
    config = create_classification_benchmark_config(
        name="insurance_classification_benchmark",
        dataset_configs=dataset_configs,
        workflow_params={
            "collection_name": "insurance_examples",
            "num_examples": 3,
            "temperature": 0.0,
        },
        execution_params={
            "concurrency_level": 2,
            "warmup_requests": 3,
            "use_mock_services": True,
            "mock_service_delay": 0.05,
        }
    )
    
    return config
