"""
Configuration schema for benchmarking system.

This module defines the configuration models and validation for benchmark
execution parameters, dataset specifications, and metrics collection.
"""

from typing import Dict, List, Optional, Union, Any, Tuple
from enum import Enum
from pydantic import BaseModel, Field, validator
from pathlib import Path


class DatasetType(str, Enum):
    """Types of datasets supported by the benchmarking system."""
    REAL = "real"
    SYNTHETIC = "synthetic"


class MetricType(str, Enum):
    """Types of metrics that can be collected during benchmarking."""
    LATENCY = "latency"
    THROUGHPUT = "throughput"
    ACCURACY = "accuracy"
    MEMORY_USAGE = "memory_usage"
    CPU_USAGE = "cpu_usage"
    ERROR_RATE = "error_rate"


class OutputFormat(str, Enum):
    """Supported output formats for benchmark results."""
    JSON = "json"
    CSV = "csv"
    YAML = "yaml"


class DatasetConfig(BaseModel):
    """Configuration for a benchmark dataset."""
    
    name: str = Field(..., description="Unique name for the dataset")
    type: DatasetType = Field(..., description="Type of dataset (real or synthetic)")
    path: Optional[str] = Field(None, description="Path to dataset file (for real datasets)")
    size: Optional[int] = Field(None, description="Number of samples (for synthetic datasets)")
    categories: List[str] = Field(..., description="List of classification categories")
    
    # Synthetic dataset generation parameters
    text_length_range: Optional[Tuple[int, int]] = Field(
        (50, 500), description="Range of text lengths for synthetic data"
    )
    class_distribution: Optional[Dict[str, float]] = Field(
        None, description="Class distribution for synthetic data (defaults to uniform)"
    )
    domain: Optional[str] = Field(
        "general", description="Domain for synthetic text generation"
    )
    
    @validator('class_distribution')
    def validate_class_distribution(cls, v, values):
        """Validate that class distribution sums to 1.0 and covers all categories."""
        if v is None:
            return v
        
        categories = values.get('categories', [])
        if not categories:
            return v
            
        # Check that all categories are covered
        missing_categories = set(categories) - set(v.keys())
        if missing_categories:
            raise ValueError(f"Missing categories in distribution: {missing_categories}")
        
        # Check that distribution sums to approximately 1.0
        total = sum(v.values())
        if abs(total - 1.0) > 0.01:
            raise ValueError(f"Class distribution must sum to 1.0, got {total}")
        
        return v


class ExecutionConfig(BaseModel):
    """Configuration for benchmark execution parameters."""
    
    batch_size: int = Field(1, description="Batch size for processing")
    concurrency_level: int = Field(1, description="Number of concurrent requests")
    max_requests: Optional[int] = Field(None, description="Maximum number of requests to process")
    timeout_seconds: float = Field(300.0, description="Timeout for individual requests")
    warmup_requests: int = Field(10, description="Number of warmup requests before measurement")
    
    # Request arrival patterns
    request_pattern: str = Field("constant", description="Request arrival pattern (constant, poisson, burst)")
    request_rate: Optional[float] = Field(None, description="Requests per second (for rate-limited patterns)")
    
    # Service configuration
    use_mock_services: bool = Field(True, description="Whether to use mock services")
    mock_service_delay: float = Field(0.1, description="Artificial delay for mock services (seconds)")


class MetricsConfig(BaseModel):
    """Configuration for metrics collection."""
    
    enabled_metrics: List[MetricType] = Field(
        default_factory=lambda: [MetricType.LATENCY, MetricType.THROUGHPUT, MetricType.ACCURACY],
        description="List of metrics to collect"
    )
    
    # Sampling configuration
    sample_rate: float = Field(1.0, description="Sampling rate for metrics collection (0.0-1.0)")
    collection_interval: float = Field(1.0, description="Interval for periodic metrics collection (seconds)")
    
    # Memory and CPU monitoring
    monitor_system_resources: bool = Field(True, description="Whether to monitor system resources")
    resource_collection_interval: float = Field(0.5, description="System resource collection interval (seconds)")


class OutputConfig(BaseModel):
    """Configuration for benchmark output and reporting."""
    
    output_dir: str = Field("benchmarks/results", description="Directory for benchmark results")
    output_formats: List[OutputFormat] = Field(
        default_factory=lambda: [OutputFormat.JSON, OutputFormat.CSV],
        description="Output formats for results"
    )
    
    # Report generation
    generate_report: bool = Field(True, description="Whether to generate detailed reports")
    include_raw_data: bool = Field(False, description="Whether to include raw measurement data")
    
    # Baseline comparison
    compare_to_baseline: bool = Field(True, description="Whether to compare against baseline")
    baseline_path: Optional[str] = Field(None, description="Path to baseline results file")
    regression_threshold: float = Field(0.1, description="Threshold for regression detection (10% by default)")


class BenchmarkConfig(BaseModel):
    """Complete configuration for a benchmark run."""
    
    name: str = Field(..., description="Name of the benchmark")
    description: Optional[str] = Field(None, description="Description of the benchmark")
    workflow_name: str = Field(..., description="Name of the workflow to benchmark")
    
    # Dataset configurations
    datasets: List[DatasetConfig] = Field(..., description="List of datasets to benchmark against")
    
    # Execution configuration
    execution: ExecutionConfig = Field(default_factory=ExecutionConfig)
    
    # Metrics configuration
    metrics: MetricsConfig = Field(default_factory=MetricsConfig)
    
    # Output configuration
    output: OutputConfig = Field(default_factory=OutputConfig)
    
    # Workflow-specific parameters
    workflow_params: Dict[str, Any] = Field(
        default_factory=dict, description="Workflow-specific parameters"
    )
    
    @validator('datasets')
    def validate_datasets(cls, v):
        """Validate that dataset names are unique."""
        names = [dataset.name for dataset in v]
        if len(names) != len(set(names)):
            raise ValueError("Dataset names must be unique")
        return v


def load_benchmark_config(config_path: Union[str, Path]) -> BenchmarkConfig:
    """Load benchmark configuration from a YAML or JSON file."""
    import yaml
    import json
    
    config_path = Path(config_path)
    
    if not config_path.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    with open(config_path, 'r') as f:
        if config_path.suffix.lower() in ['.yaml', '.yml']:
            data = yaml.safe_load(f)
        elif config_path.suffix.lower() == '.json':
            data = json.load(f)
        else:
            raise ValueError(f"Unsupported configuration file format: {config_path.suffix}")
    
    return BenchmarkConfig(**data)
