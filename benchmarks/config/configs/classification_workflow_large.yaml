# Large-scale classification workflow benchmark configuration
name: "classification_workflow_large"
description: "Large-scale performance benchmark for RAG Chain of Thought classification workflow"
workflow_name: "rag_chain_of_thought"

# Dataset configurations
datasets:
  - name: "synthetic_insurance_large"
    type: "synthetic"
    size: 1000
    categories:
      - "Billing Inquiries"
      - "Policy Administration"
      - "Claims Assistance"
      - "Coverage Explanations"
      - "Quotes and Proposals"
      - "Account Management"
      - "Billing Disputes"
      - "Claims Disputes"
      - "Policy Comparisons"
      - "General Inquiries"
    domain: "insurance"
    text_length_range: [50, 600]
    # Realistic class distribution based on typical insurance queries
    class_distribution:
      "Billing Inquiries": 0.25
      "Policy Administration": 0.15
      "Claims Assistance": 0.20
      "Coverage Explanations": 0.15
      "Quotes and Proposals": 0.10
      "Account Management": 0.05
      "Billing Disputes": 0.03
      "Claims Disputes": 0.03
      "Policy Comparisons": 0.02
      "General Inquiries": 0.02

  - name: "synthetic_stress_test"
    type: "synthetic"
    size: 500
    categories:
      - "Billing Inquiries"
      - "Claims Assistance"
      - "Coverage Explanations"
    domain: "insurance"
    text_length_range: [10, 1000]  # Extreme range for stress testing
    class_distribution:
      "Billing Inquiries": 0.4
      "Claims Assistance": 0.4
      "Coverage Explanations": 0.2

# Execution configuration
execution:
  batch_size: 1
  concurrency_level: 5
  max_requests: 500  # Limit for large benchmark
  timeout_seconds: 300.0
  warmup_requests: 10
  request_pattern: "poisson"  # More realistic arrival pattern
  request_rate: 5.0  # 5 requests per second
  use_mock_services: true
  mock_service_delay: 0.2  # Higher delay to simulate real service latency

# Metrics configuration
metrics:
  enabled_metrics:
    - "latency"
    - "throughput"
    - "accuracy"
    - "error_rate"
    - "memory_usage"
    - "cpu_usage"
  sample_rate: 0.8  # Sample 80% of requests for performance
  collection_interval: 0.5
  monitor_system_resources: true
  resource_collection_interval: 0.25

# Output configuration
output:
  output_dir: "benchmarks/results"
  output_formats:
    - "json"
    - "csv"
    - "yaml"
  generate_report: true
  include_raw_data: true
  compare_to_baseline: true
  regression_threshold: 0.2  # More lenient for large-scale tests

# Workflow-specific parameters
workflow_params:
  collection_name: "insurance_examples"
  num_examples: 7
  max_tokens: 4096
  temperature: 0.0
  llm_model: "Qwen/Qwen2.5-1.5B-Instruct"
  embedding_model: "BAAI/bge-reranker-large"
