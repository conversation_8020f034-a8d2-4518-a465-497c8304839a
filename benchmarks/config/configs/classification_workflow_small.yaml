# Small-scale classification workflow benchmark configuration
name: "classification_workflow_small"
description: "Small-scale benchmark for RAG Chain of Thought classification workflow"
workflow_name: "rag_chain_of_thought"

# Dataset configurations
datasets:
  - name: "real_insurance_small"
    type: "real"
    path: ".cache/data/classification_workflow/dataset.csv"
    categories:
      - "Billing Inquiries"
      - "Policy Administration"
      - "Claims Assistance"
      - "Coverage Explanations"
      - "Quotes and Proposals"
      - "Account Management"
      - "Billing Disputes"
      - "Claims Disputes"
      - "Policy Comparisons"
      - "General Inquiries"

  - name: "synthetic_insurance_small"
    type: "synthetic"
    size: 50
    categories:
      - "Billing Inquiries"
      - "Policy Administration"
      - "Claims Assistance"
      - "Coverage Explanations"
      - "Account Management"
    domain: "insurance"
    text_length_range: [50, 200]
    class_distribution:
      "Billing Inquiries": 0.3
      "Policy Administration": 0.2
      "Claims Assistance": 0.2
      "Coverage Explanations": 0.2
      "Account Management": 0.1

# Execution configuration
execution:
  batch_size: 1
  concurrency_level: 1
  max_requests: 20  # Limit for small benchmark
  timeout_seconds: 60.0
  warmup_requests: 3
  request_pattern: "constant"
  use_mock_services: true
  mock_service_delay: 0.05

# Metrics configuration
metrics:
  enabled_metrics:
    - "latency"
    - "throughput"
    - "accuracy"
    - "error_rate"
    - "memory_usage"
  sample_rate: 1.0
  collection_interval: 1.0
  monitor_system_resources: true
  resource_collection_interval: 0.5

# Output configuration
output:
  output_dir: "benchmarks/results"
  output_formats:
    - "json"
    - "csv"
  generate_report: true
  include_raw_data: false
  compare_to_baseline: true
  regression_threshold: 0.1

# Workflow-specific parameters
workflow_params:
  collection_name: "insurance_examples"
  num_examples: 3
  max_tokens: 2048
  temperature: 0.0
  llm_model: "Qwen/Qwen2.5-1.5B-Instruct"
  embedding_model: "BAAI/bge-reranker-large"
