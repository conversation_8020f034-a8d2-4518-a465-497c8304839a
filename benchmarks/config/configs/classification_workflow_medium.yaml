# Medium-scale classification workflow benchmark configuration
name: "classification_workflow_medium"
description: "Medium-scale benchmark for RAG Chain of Thought classification workflow with concurrency"
workflow_name: "rag_chain_of_thought"

# Dataset configurations
datasets:
  - name: "real_insurance_full"
    type: "real"
    path: ".cache/data/classification_workflow/dataset.csv"
    categories:
      - "Billing Inquiries"
      - "Policy Administration"
      - "Claims Assistance"
      - "Coverage Explanations"
      - "Quotes and Proposals"
      - "Account Management"
      - "Billing Disputes"
      - "Claims Disputes"
      - "Policy Comparisons"
      - "General Inquiries"

  - name: "synthetic_insurance_medium"
    type: "synthetic"
    size: 200
    categories:
      - "Billing Inquiries"
      - "Policy Administration"
      - "Claims Assistance"
      - "Coverage Explanations"
      - "Quotes and Proposals"
      - "Account Management"
      - "Billing Disputes"
      - "Claims Disputes"
    domain: "insurance"
    text_length_range: [100, 400]
    # Uniform distribution (default)

  - name: "synthetic_edge_cases"
    type: "synthetic"
    size: 100
    categories:
      - "Billing Inquiries"
      - "Claims Assistance"
      - "Coverage Explanations"
    domain: "insurance"
    text_length_range: [20, 800]  # Wide range for edge cases
    class_distribution:
      "Billing Inquiries": 0.5
      "Claims Assistance": 0.3
      "Coverage Explanations": 0.2

# Execution configuration
execution:
  batch_size: 1
  concurrency_level: 3
  max_requests: 100
  timeout_seconds: 120.0
  warmup_requests: 5
  request_pattern: "constant"
  request_rate: 2.0  # 2 requests per second
  use_mock_services: true
  mock_service_delay: 0.1

# Metrics configuration
metrics:
  enabled_metrics:
    - "latency"
    - "throughput"
    - "accuracy"
    - "error_rate"
    - "memory_usage"
    - "cpu_usage"
  sample_rate: 1.0
  collection_interval: 1.0
  monitor_system_resources: true
  resource_collection_interval: 0.5

# Output configuration
output:
  output_dir: "benchmarks/results"
  output_formats:
    - "json"
    - "csv"
    - "yaml"
  generate_report: true
  include_raw_data: true
  compare_to_baseline: true
  regression_threshold: 0.15

# Workflow-specific parameters
workflow_params:
  collection_name: "insurance_examples"
  num_examples: 5
  max_tokens: 4096
  temperature: 0.0
  llm_model: "Qwen/Qwen2.5-1.5B-Instruct"
  embedding_model: "BAAI/bge-reranker-large"
