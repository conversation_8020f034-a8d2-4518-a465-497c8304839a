# LegoAgent Benchmarking System

A comprehensive benchmarking framework for evaluating classification workflows with support for multi-dataset testing, synthetic data generation, and performance regression detection.

## Features

- **Multi-dataset support**: Test workflows against multiple datasets (small/medium/large, different domains)
- **Synthetic dataset generation**: Generate configurable synthetic datasets for testing edge cases
- **Comprehensive metrics**: Latency, throughput, accuracy, memory usage, CPU usage, error rates
- **Async execution**: Full async support with configurable concurrency levels
- **Service mocking**: Mock LLM services for reproducible benchmarks
- **Regression detection**: Compare results against baselines to detect performance regressions
- **Multiple output formats**: JSON, CSV, YAML, and HTML reports
- **Configurable execution**: YAML/JSON configuration files for benchmark parameters

## Quick Start

### 1. Basic Usage

```python
import asyncio
from benchmarks.workflows.classification_benchmark import (
    create_classification_benchmark_config,
    run_classification_benchmark
)

# Create a simple benchmark configuration
config = create_classification_benchmark_config(
    name="my_benchmark",
    dataset_configs=[
        {
            "name": "test_dataset",
            "type": "real",
            "path": "path/to/dataset.csv",
            "categories": ["Category1", "Category2", "Category3"]
        }
    ]
)

# Run the benchmark
results = await run_classification_benchmark(config)
print(f"Benchmark completed: {results['success']}")
```

### 2. Using Configuration Files

```bash
# Run benchmark from YAML configuration
python -m benchmarks.example_benchmark
```

```yaml
# Example configuration file
name: "classification_benchmark"
workflow_name: "rag_chain_of_thought"

datasets:
  - name: "real_dataset"
    type: "real"
    path: "datasets/real/data.csv"
    categories: ["Cat1", "Cat2", "Cat3"]
  
  - name: "synthetic_dataset"
    type: "synthetic"
    size: 100
    categories: ["Cat1", "Cat2", "Cat3"]
    domain: "insurance"

execution:
  concurrency_level: 2
  max_requests: 50
  use_mock_services: true

metrics:
  enabled_metrics: ["latency", "throughput", "accuracy"]

output:
  output_formats: ["json", "csv"]
  generate_report: true
```

## Directory Structure

```
benchmarks/
├── __init__.py                 # Main package
├── README.md                   # This documentation
├── example_benchmark.py        # Example usage script
├── core/                       # Core framework components
│   ├── benchmark_runner.py     # Main execution engine
│   ├── dataset_manager.py      # Dataset loading and management
│   ├── synthetic_generator.py  # Synthetic data generation
│   ├── metrics_collector.py    # Performance metrics collection
│   └── result_formatter.py     # Result output formatting
├── config/                     # Configuration system
│   ├── benchmark_config.py     # Configuration schemas
│   └── configs/                # Example configurations
│       ├── classification_workflow_small.yaml
│       ├── classification_workflow_medium.yaml
│       └── classification_workflow_large.yaml
├── workflows/                  # Workflow-specific implementations
│   └── classification_benchmark.py
├── utils/                      # Utility modules
│   ├── async_utils.py          # Async utilities
│   └── regression_detector.py  # Performance regression detection
├── datasets/                   # Dataset storage
│   ├── real/                   # Real datasets
│   └── synthetic/              # Generated synthetic datasets
└── results/                    # Benchmark outputs
    ├── baselines/              # Baseline performance metrics
    └── reports/                # Generated reports
```

## Configuration

### Dataset Configuration

#### Real Datasets
```yaml
datasets:
  - name: "my_real_dataset"
    type: "real"
    path: "path/to/dataset.csv"  # CSV with 'text' and 'label' columns
    categories: ["Category1", "Category2", "Category3"]
```

#### Synthetic Datasets
```yaml
datasets:
  - name: "my_synthetic_dataset"
    type: "synthetic"
    size: 500
    categories: ["Category1", "Category2", "Category3"]
    domain: "insurance"  # or "general"
    text_length_range: [50, 300]
    class_distribution:
      "Category1": 0.5
      "Category2": 0.3
      "Category3": 0.2
```

### Execution Configuration

```yaml
execution:
  batch_size: 1
  concurrency_level: 4          # Number of concurrent requests
  max_requests: 100             # Limit total requests
  timeout_seconds: 300.0
  warmup_requests: 10           # Requests to ignore in metrics
  request_pattern: "constant"   # or "poisson", "burst"
  request_rate: 5.0            # Requests per second
  use_mock_services: true
  mock_service_delay: 0.1
```

### Metrics Configuration

```yaml
metrics:
  enabled_metrics:
    - "latency"
    - "throughput"
    - "accuracy"
    - "error_rate"
    - "memory_usage"
    - "cpu_usage"
  sample_rate: 1.0              # 0.0-1.0, fraction of requests to sample
  monitor_system_resources: true
```

### Output Configuration

```yaml
output:
  output_dir: "benchmarks/results"
  output_formats: ["json", "csv", "yaml"]
  generate_report: true         # Generate HTML report
  include_raw_data: false       # Include raw measurement data
  compare_to_baseline: true
  regression_threshold: 0.1     # 10% threshold for regression detection
```

## Metrics

The benchmarking system collects the following metrics:

- **Latency**: Request processing time (seconds)
- **Throughput**: Requests processed per second
- **Accuracy**: Classification accuracy (0.0-1.0)
- **Error Rate**: Percentage of failed requests
- **Memory Usage**: System memory utilization (%)
- **CPU Usage**: System CPU utilization (%)

Each metric includes statistical aggregations:
- Count, Min, Max, Mean, Median
- 95th and 99th percentiles
- Standard deviation

## Synthetic Data Generation

The system can generate synthetic datasets with configurable parameters:

### Insurance Domain Templates
- Billing Inquiries
- Policy Administration
- Claims Assistance
- Coverage Explanations
- Account Management

### General Domain Templates
- Positive sentiment
- Negative sentiment
- Neutral sentiment

### Customization Options
- Text length ranges
- Class distributions
- Domain-specific keywords
- Template variations

## Performance Regression Detection

The system automatically detects performance regressions by comparing results with baselines:

### Severity Levels
- **Minor**: 5-15% degradation
- **Moderate**: 15-30% degradation
- **Severe**: 30-50% degradation
- **Critical**: >50% degradation

### Regression Rules
- **Latency/Error Rate**: Increases are regressions
- **Throughput/Accuracy**: Decreases are regressions
- **Memory/CPU Usage**: Increases are regressions

## Adding New Benchmarks

### 1. Create Configuration File

```yaml
# benchmarks/config/configs/my_benchmark.yaml
name: "my_custom_benchmark"
workflow_name: "my_workflow"
# ... rest of configuration
```

### 2. Register Workflow (if needed)

```python
from benchmarks.core.benchmark_runner import BenchmarkRunner

runner = BenchmarkRunner(config)
runner.register_workflow(
    "my_workflow",
    sync_func=my_sync_function,
    async_func=my_async_function
)
```

### 3. Run Benchmark

```python
results = await runner.run_benchmark()
```

## Output Formats

### JSON Output
Complete benchmark results with all metrics and metadata.

### CSV Output
Flattened metrics suitable for spreadsheet analysis.

### HTML Report
Human-readable report with charts and summaries.

### YAML Output
Structured results in YAML format.

## Best Practices

1. **Use Mock Services**: Enable `use_mock_services: true` for reproducible results
2. **Warmup Requests**: Include warmup requests to account for cold start effects
3. **Multiple Datasets**: Test with different dataset sizes and characteristics
4. **Baseline Comparison**: Maintain baselines for regression detection
5. **Resource Monitoring**: Enable system resource monitoring for comprehensive analysis
6. **Concurrency Testing**: Test different concurrency levels to find optimal performance

## Troubleshooting

### Common Issues

1. **Dataset Not Found**: Ensure dataset paths are correct and files exist
2. **Service Connection Errors**: Use mock services for isolated testing
3. **Memory Issues**: Reduce dataset size or concurrency level
4. **Timeout Errors**: Increase timeout_seconds in configuration

### Debug Mode

Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Examples

See `benchmarks/example_benchmark.py` for comprehensive usage examples including:
- Small-scale quick tests
- Configuration file usage
- Synthetic dataset generation
- Performance comparisons
- Concurrency testing
