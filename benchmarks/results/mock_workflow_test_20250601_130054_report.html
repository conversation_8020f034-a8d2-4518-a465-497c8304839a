
<!DOCTYPE html>
<html>
<head>
    <title>Benchmark Report: mock_workflow_test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .metric-table { border-collapse: collapse; width: 100%; }
        .metric-table th, .metric-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .metric-table th { background-color: #f2f2f2; }
        .summary-box { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { color: #d32f2f; }
        .success { color: #388e3c; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Benchmark Report: mock_workflow_test</h1>
        <p><strong>Workflow:</strong> mock_workflow</p>
        <p><strong>Timestamp:</strong> 2025-06-01T13:00:54.948888</p>
        <p><strong>Description:</strong> Classification workflow benchmark: mock_workflow_test</p>
    </div>
    
    <div class="section">
        <h2>Execution Summary</h2>
        <div class="summary-box">
            <p><strong>Duration:</strong> 0.05 seconds</p>
            <p><strong>Total Requests:</strong> 4</p>
            <p><strong>Throughput:</strong> 7.95 requests/second</p>
            <p class="success">
                <strong>Error Rate:</strong> 0.00%
            </p>
        </div>
    </div>
    
    <div class="section">
        <h2>Performance Metrics</h2>
        <table class="metric-table">
            <thead>
                <tr>
                    <th>Metric</th>
                    <th>Count</th>
                    <th>Mean</th>
                    <th>Median</th>
                    <th>Min</th>
                    <th>Max</th>
                    <th>P95</th>
                    <th>P99</th>
                    <th>Std Dev</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td>cpu_usage</td>
                    <td>1</td>
                    <td>0.0000</td>
                    <td>0.0000</td>
                    <td>0.0000</td>
                    <td>0.0000</td>
                    <td>0.0000</td>
                    <td>0.0000</td>
                    <td>0.0000</td>
                </tr>
            
                <tr>
                    <td>memory_usage</td>
                    <td>1</td>
                    <td>80.4000</td>
                    <td>80.4000</td>
                    <td>80.4000</td>
                    <td>80.4000</td>
                    <td>80.4000</td>
                    <td>80.4000</td>
                    <td>0.0000</td>
                </tr>
            
                <tr>
                    <td>latency</td>
                    <td>4</td>
                    <td>0.0101</td>
                    <td>0.0101</td>
                    <td>0.0101</td>
                    <td>0.0101</td>
                    <td>0.0101</td>
                    <td>0.0101</td>
                    <td>0.0000</td>
                </tr>
            
                <tr>
                    <td>accuracy</td>
                    <td>1</td>
                    <td>0.2500</td>
                    <td>0.2500</td>
                    <td>0.2500</td>
                    <td>0.2500</td>
                    <td>0.2500</td>
                    <td>0.2500</td>
                    <td>0.0000</td>
                </tr>
            
                <tr>
                    <td>throughput</td>
                    <td>1</td>
                    <td>76.7130</td>
                    <td>76.7130</td>
                    <td>76.7130</td>
                    <td>76.7130</td>
                    <td>76.7130</td>
                    <td>76.7130</td>
                    <td>0.0000</td>
                </tr>
            
                <tr>
                    <td>error_rate</td>
                    <td>1</td>
                    <td>0.0000</td>
                    <td>0.0000</td>
                    <td>0.0000</td>
                    <td>0.0000</td>
                    <td>0.0000</td>
                    <td>0.0000</td>
                    <td>0.0000</td>
                </tr>
            
            </tbody>
        </table>
    </div>
    
    <div class="section">
        <h2>Configuration</h2>
        <pre style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;">
        {
  "name": "mock_workflow_test",
  "description": "Classification workflow benchmark: mock_workflow_test",
  "workflow_name": "mock_workflow",
  "datasets": [
    {
      "name": "small_synthetic_test",
      "type": "synthetic",
      "path": null,
      "size": 5,
      "categories": [
        "Category1",
        "Category2",
        "Category3"
      ],
      "text_length_range": [
        50,
        100
      ],
      "class_distribution": null,
      "domain": "general"
    }
  ],
  "execution": {
    "batch_size": 1,
    "concurrency_level": 1,
    "max_requests": 5,
    "timeout_seconds": 300.0,
    "warmup_requests": 1,
    "request_pattern": "constant",
    "request_rate": null,
    "use_mock_services": true,
    "mock_service_delay": 0.01
  },
  "metrics": {
    "enabled_metrics": [
      "latency",
      "throughput",
      "accuracy",
      "error_rate",
      "memory_usage",
      "cpu_usage"
    ],
    "sample_rate": 1.0,
    "collection_interval": 1.0,
    "monitor_system_resources": true,
    "resource_collection_interval": 0.5
  },
  "output": {
    "output_dir": "benchmarks/results",
    "output_formats": [
      "json",
      "csv"
    ],
    "generate_report": true,
    "include_raw_data": false,
    "compare_to_baseline": true,
    "baseline_path": null,
    "regression_threshold": 0.1
  },
  "workflow_params": {
    "collection_name": "simple",
    "num_examples": 5,
    "max_tokens": 4096,
    "temperature": 0.0
  }
}
        </pre>
    </div>
</body>
</html>
        