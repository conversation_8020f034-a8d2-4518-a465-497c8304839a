#!/usr/bin/env python3
"""
Test script for the benchmarking system.

This script tests the basic functionality of the benchmarking system
without requiring the full classification workflow to be set up.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_dataset_manager():
    """Test the dataset manager functionality."""
    logger.info("Testing dataset manager...")
    
    from benchmarks.core.dataset_manager import DatasetManager
    from benchmarks.config.benchmark_config import DatasetConfig, DatasetType
    
    # Test loading real dataset
    dataset_config = DatasetConfig(
        name="test_real_dataset",
        type=DatasetType.REAL,
        path="benchmarks/datasets/real/insurance_classification_dataset.csv",
        categories=[
            "Billing Inquiries",
            "Policy Administration",
            "Claims Assistance",
            "Coverage Explanations",
            "Account Management"
        ]
    )
    
    manager = DatasetManager()
    
    try:
        samples = manager.load_dataset(dataset_config)
        logger.info(f"Successfully loaded {len(samples)} samples")
        
        # Print first few samples
        for i, sample in enumerate(samples[:3]):
            logger.info(f"Sample {i+1}: {sample.text[:100]}... -> {sample.label}")
        
        # Get dataset stats
        stats = manager.get_dataset_stats(dataset_config)
        logger.info(f"Dataset stats: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"Dataset manager test failed: {e}")
        return False


async def test_synthetic_generator():
    """Test the synthetic dataset generator."""
    logger.info("Testing synthetic dataset generator...")
    
    from benchmarks.core.synthetic_generator import SyntheticDatasetGenerator
    from benchmarks.config.benchmark_config import DatasetConfig, DatasetType
    
    # Test synthetic dataset generation
    dataset_config = DatasetConfig(
        name="test_synthetic_dataset",
        type=DatasetType.SYNTHETIC,
        size=10,
        categories=["Billing Inquiries", "Claims Assistance", "Coverage Explanations"],
        domain="insurance",
        text_length_range=(50, 200),
        class_distribution={
            "Billing Inquiries": 0.5,
            "Claims Assistance": 0.3,
            "Coverage Explanations": 0.2
        }
    )
    
    generator = SyntheticDatasetGenerator(seed=42)
    
    try:
        samples = generator.generate_dataset(dataset_config)
        logger.info(f"Successfully generated {len(samples)} synthetic samples")
        
        # Print generated samples
        for i, sample in enumerate(samples):
            logger.info(f"Generated sample {i+1}: {sample.text[:80]}... -> {sample.label}")
        
        # Check distribution
        label_counts = {}
        for sample in samples:
            label_counts[sample.label] = label_counts.get(sample.label, 0) + 1
        
        logger.info(f"Generated distribution: {label_counts}")
        
        return True
        
    except Exception as e:
        logger.error(f"Synthetic generator test failed: {e}")
        return False


async def test_metrics_collector():
    """Test the metrics collector."""
    logger.info("Testing metrics collector...")
    
    from benchmarks.core.metrics_collector import MetricsCollector
    from benchmarks.config.benchmark_config import MetricsConfig, MetricType
    
    # Create metrics config
    config = MetricsConfig(
        enabled_metrics=[MetricType.LATENCY, MetricType.ACCURACY, MetricType.ERROR_RATE],
        sample_rate=1.0,
        monitor_system_resources=False  # Disable for testing
    )
    
    collector = MetricsCollector(config)
    
    try:
        # Start collection
        collector.start_collection()
        
        # Simulate some metrics
        for i in range(5):
            request_id = collector.start_request()
            
            # Simulate processing time
            await asyncio.sleep(0.01)
            
            # Record latency
            collector.record_latency(0.01 + i * 0.005, {"request": i})
            
            # Record accuracy (simulate some correct predictions)
            accuracy = 0.8 if i % 2 == 0 else 0.9
            collector.record_accuracy(accuracy)
            
            collector.end_request(request_id, success=True)
        
        # Simulate an error
        error_request_id = collector.start_request()
        collector.record_error({"error_type": "test_error"})
        collector.end_request(error_request_id, success=False)
        
        # Stop collection
        collector.stop_collection()
        
        # Get summary
        summary = collector.get_summary()
        logger.info(f"Metrics summary: {summary}")
        
        # Get aggregated metrics
        aggregated = collector.aggregate_metrics()
        for metric_type, metrics in aggregated.items():
            logger.info(f"{metric_type.value}: {metrics.to_dict()}")
        
        return True
        
    except Exception as e:
        logger.error(f"Metrics collector test failed: {e}")
        return False


async def test_configuration_loading():
    """Test configuration loading."""
    logger.info("Testing configuration loading...")
    
    from benchmarks.config.benchmark_config import load_benchmark_config
    
    config_path = Path("benchmarks/config/configs/classification_workflow_small.yaml")
    
    try:
        config = load_benchmark_config(config_path)
        logger.info(f"Successfully loaded config: {config.name}")
        logger.info(f"Workflow: {config.workflow_name}")
        logger.info(f"Datasets: {[d.name for d in config.datasets]}")
        logger.info(f"Execution config: concurrency={config.execution.concurrency_level}")
        
        return True
        
    except Exception as e:
        logger.error(f"Configuration loading test failed: {e}")
        return False


async def test_mock_workflow():
    """Test with a mock workflow function."""
    logger.info("Testing with mock workflow...")
    
    from benchmarks.core.benchmark_runner import BenchmarkRunner
    from benchmarks.workflows.classification_benchmark import create_classification_benchmark_config
    from benchmarks.core.dataset_manager import DatasetSample
    
    # Create a simple mock workflow
    def mock_sync_workflow(sample: DatasetSample, dataset_config, **kwargs) -> str:
        """Mock synchronous workflow that returns a random category."""
        import random
        return random.choice(dataset_config.categories)
    
    async def mock_async_workflow(sample: DatasetSample, dataset_config, **kwargs) -> str:
        """Mock asynchronous workflow that returns a random category."""
        import random
        await asyncio.sleep(0.01)  # Simulate processing
        return random.choice(dataset_config.categories)
    
    # Create a simple benchmark config
    config = create_classification_benchmark_config(
        name="mock_workflow_test",
        dataset_configs=[
            {
                "name": "small_synthetic_test",
                "type": "synthetic",
                "size": 5,
                "categories": ["Category1", "Category2", "Category3"],
                "domain": "general",
                "text_length_range": [50, 100]
            }
        ],
        execution_params={
            "max_requests": 5,
            "concurrency_level": 1,
            "warmup_requests": 1,
            "use_mock_services": True,
            "mock_service_delay": 0.01,
        }
    )
    
    try:
        # Create benchmark runner
        runner = BenchmarkRunner(config)
        
        # Register mock workflows
        runner.register_workflow(
            "mock_workflow",
            sync_func=mock_sync_workflow,
            async_func=mock_async_workflow
        )
        
        # Update config to use mock workflow
        runner.config.workflow_name = "mock_workflow"
        
        # Run benchmark
        results = await runner.run_benchmark()
        
        logger.info(f"Mock workflow benchmark completed: {results['success']}")
        if results['success']:
            logger.info(f"Metrics summary: {results['metrics_summary']}")
            logger.info(f"Dataset results: {len(results['dataset_results'])} datasets processed")
        
        return results['success']
        
    except Exception as e:
        logger.error(f"Mock workflow test failed: {e}")
        return False


async def main():
    """Run all tests."""
    logger.info("Starting benchmarking system tests...")
    
    tests = [
        ("Dataset Manager", test_dataset_manager),
        ("Synthetic Generator", test_synthetic_generator),
        ("Metrics Collector", test_metrics_collector),
        ("Configuration Loading", test_configuration_loading),
        ("Mock Workflow", test_mock_workflow),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running test: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            success = await test_func()
            results[test_name] = success
            status = "PASSED" if success else "FAILED"
            logger.info(f"Test {test_name}: {status}")
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Print summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "PASSED" if success else "FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Benchmarking system is working correctly.")
    else:
        logger.warning(f"⚠️  {total - passed} test(s) failed. Please check the logs above.")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
