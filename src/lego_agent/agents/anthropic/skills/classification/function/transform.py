"""
Transform functions for classification.

This module provides functions for transforming and parsing classification responses.
"""

import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)


def extract_classification_response(
    response_text: str, categories: List[str]
) -> Dict[str, str]:
    """Parse the classification response.

    This function tries multiple strategies to extract the category and reasoning
    from the LLM response, supporting different response formats.

    Args:
        response_text: The response text from the LLM
        categories: List of valid categories to match against

    Returns:
        Dictionary containing the reasoning and category
    """
    logger.debug(f"Extracting classification from response: {response_text}")

    # If response is empty or None
    if not response_text or not response_text.strip():
        logger.warning("Empty or None response text received")
        return {"reasoning": "Empty response from LLM", "category": "Unknown"}

    # Try XML format with <scratchpad> and <category> tags
    try:
        scratchpad_end = response_text.find("</scratchpad>")
        category_start = response_text.find("<category>")

        if scratchpad_end != -1 and category_start != -1:
            reasoning = response_text[
                response_text.find("<scratchpad>")
                + len("<scratchpad>") : scratchpad_end
            ].strip()
            category = response_text[
                category_start
                + len("<category>") : response_text.find("</category>", category_start)
            ].strip()

            # Validate category against provided categories
            if category and reasoning:
                if category in categories:
                    return {
                        "reasoning": reasoning,
                        "category": category,
                    }
                else:
                    logger.warning(
                        f"Found category '{category}' not in provided categories list"
                    )
    except Exception as e:
        logger.debug(f"Failed to parse XML format: {str(e)}")

    # Try to find a category in the response by looking for **bold** text
    import re

    bold_pattern = r"\*\*(.*?)\*\*"
    bold_matches = re.findall(bold_pattern, response_text)

    if bold_matches:
        # Get the last bold match (often the conclusion)
        category = bold_matches[-1].strip()
        # Check if the bold text matches any of the provided categories
        for valid_category in categories:
            if category.lower() == valid_category.lower():
                return {"reasoning": response_text, "category": valid_category}

    # Try to find the category by looking for exact matches in the text
    for category in categories:
        if category.lower() in response_text.lower():
            return {"reasoning": response_text, "category": category}

    # If we get here, we couldn't parse the response
    logger.warning(f"Could not parse classification response: {response_text}")
    return {
        "reasoning": response_text or "No reasoning provided",
        "category": "Unknown",
    }
