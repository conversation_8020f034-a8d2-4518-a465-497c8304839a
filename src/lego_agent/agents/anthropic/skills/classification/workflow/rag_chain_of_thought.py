"""
RAG Chain of Thought classification workflow.

This module provides workflow implementations for text classification
using RAG with Chain of Thought reasoning.
"""

from typing import List, Dict, Any, Optional
import logging
from pydantic import Field

from lego_agent.core.schema.io import TaskRequest, TaskResponse
from lego_agent.core.config import config_manager

# Import task components for direct usage
from lego_agent.agents.anthropic.skills.classification.task.embedding_task import (
    EmbeddingTaskRequest,
    embed_texts,
    embed_texts_async,
)
from lego_agent.agents.anthropic.skills.classification.task.vdb_task import (
    VDBSearchRequest,
    search_vdb,
    search_vdb_async,
)
from lego_agent.agents.anthropic.skills.classification.task.llm_task import (
    ClassificationLLMRequest,
    classify_with_llm,
    classify_with_llm_async,
)
from lego_agent.agents.anthropic.skills.classification.function.transform import (
    extract_classification_response,
)

logger = logging.getLogger(__name__)


# Get configuration values using the configuration manager
def get_config_value(key, default=None):
    """Get a configuration value from the classification config."""
    return config_manager.get_config_value("anthropic.classification", key, default)


class RAGChainOfThoughtWorkflowRequest(TaskRequest):
    """Request model for RAG Chain of Thought classification workflow.

    Attributes:
        text: The text to classify
        categories: List of possible categories
        collection_name: Name of the vector database collection to use
        num_examples: Number of examples to retrieve
        llm_model: Language model to use for classification
        embedding_model: Embedding model to use for retrieval
        max_tokens: Maximum tokens for generation
        temperature: Temperature for generation
    """

    task_name: str = "rag_chain_of_thought_workflow"

    text: str = Field(..., description="The text to classify")
    categories: List[str] = Field(..., description="List of possible categories")
    collection_name: str = Field(
        default=get_config_value("collection_name", "simple"),
        description="Name of the vector database collection to use",
    )
    num_examples: int = Field(
        default=get_config_value("num_examples", 5),
        description="Number of examples to retrieve",
    )
    llm_model: Optional[str] = Field(
        default=get_config_value("llm_model"),
        description="Language model to use for classification",
    )
    embedding_model: Optional[str] = Field(
        default=get_config_value("embedding_model"),
        description="Embedding model to use for retrieval",
    )
    max_tokens: int = Field(
        default=get_config_value("max_tokens", 4096),
        description="Maximum tokens for generation",
    )
    temperature: float = Field(
        default=get_config_value("temperature", 0.0),
        description="Temperature for generation",
    )


class RAGChainOfThoughtWorkflowResponse(TaskResponse):
    """Response model for RAG Chain of Thought classification workflow.

    Attributes:
        category: The predicted category
        confidence: Optional confidence score
        reasoning: Optional reasoning for the classification
    """

    task_name: str = "rag_chain_of_thought_workflow"

    category: str = Field(..., description="The predicted category")
    confidence: Optional[float] = Field(
        default=None, description="Optional confidence score"
    )
    reasoning: Optional[str] = Field(
        default=None, description="Optional reasoning for the classification"
    )

    def dict(self, *args, **kwargs):
        """Override dict method to conditionally include reasoning based on config."""
        data = super().dict(*args, **kwargs)
        if not get_config_value("show_reasoning", True):
            data.pop("reasoning", None)
        return data


def rag_chain_of_thought_classify(
    request: RAGChainOfThoughtWorkflowRequest,
) -> RAGChainOfThoughtWorkflowResponse:
    """Classify the given text using RAG with Chain of Thought reasoning.

    This workflow retrieves similar examples from a vector database,
    formats them into a prompt, and uses a language model to classify
    text with chain-of-thought reasoning.

    The workflow directly composes multiple finer-grained tasks:
    1. Embed the text using the embedding task
    2. Search for similar examples using the vector database task
    3. Create a prompt using the retrieved examples
    4. Classify the text using the LLM task
    5. Parse the response and return the classification result

    Args:
        request: The workflow request containing the text to classify and parameters

    Returns:
        Workflow response containing the classification result
    """
    logger.info(
        f"RAG Chain of Thought classification workflow started for: {request.text[:100]}..."
    )

    # Step 1: Embed the text
    embedding_request = EmbeddingTaskRequest(
        texts=[request.text],
        model=request.embedding_model,
    )

    embedding_response = embed_texts(embedding_request)

    if not embedding_response.success or not embedding_response.embeddings:
        logger.error("Failed to generate embeddings")
        return RAGChainOfThoughtWorkflowResponse(
            category="Unknown",
            reasoning=embedding_response.error or "Failed to generate embeddings",
        )

    # Step 2: Search for similar examples
    vdb_search_request = VDBSearchRequest(
        query_texts=[request.text],
        query_embeddings=embedding_response.embeddings,
        collection_name=request.collection_name,
        k=request.num_examples,
    )

    vdb_search_response = search_vdb(vdb_search_request)

    if not vdb_search_response.success:
        logger.error(f"Failed to search vector database: {vdb_search_response.error}")
        return RAGChainOfThoughtWorkflowResponse(
            category="Unknown",
            reasoning=vdb_search_response.error or "Failed to search vector database",
        )

    # Get examples from search results
    if not vdb_search_response.results or not vdb_search_response.results[0]:
        logger.warning("No similar examples found")
        examples = []
    else:
        examples = vdb_search_response.results[0]

    # Step 3 & 4: Classify using LLM (prompt creation is now handled inside the LLM task)
    llm_request = ClassificationLLMRequest(
        text=request.text,
        categories=request.categories,
        examples=examples,
        model=request.llm_model,
        max_tokens=request.max_tokens,
        temperature=request.temperature,
    )

    llm_response = classify_with_llm(llm_request)

    if not llm_response.success or not llm_response.response_text:
        logger.error(f"Failed to classify with LLM: {llm_response.error}")
        return RAGChainOfThoughtWorkflowResponse(
            category="Unknown",
            reasoning=llm_response.error or "Failed to classify with LLM",
        )

    # Step 5: Parse the response
    parsed_response = extract_classification_response(
        llm_response.response_text, request.categories
    )

    logger.info(f"Classification result: {parsed_response.get('category')}")

    # Create workflow response
    workflow_response = RAGChainOfThoughtWorkflowResponse(
        category=parsed_response.get("category", "Unknown"),
        reasoning=parsed_response.get("reasoning"),
    )

    logger.info(
        f"RAG Chain of Thought classification workflow completed with result: {workflow_response.category}"
    )

    return workflow_response


async def rag_chain_of_thought_classify_async(
    request: RAGChainOfThoughtWorkflowRequest,
) -> RAGChainOfThoughtWorkflowResponse:
    """Classify the given text using RAG with Chain of Thought reasoning asynchronously.

    This workflow retrieves similar examples from a vector database,
    formats them into a prompt, and uses a language model to classify
    text with chain-of-thought reasoning.

    The workflow directly composes multiple finer-grained async tasks:
    1. Embed the text using the embedding task
    2. Search for similar examples using the vector database task
    3. Create a prompt using the retrieved examples
    4. Classify the text using the LLM task
    5. Parse the response and return the classification result

    Args:
        request: The workflow request containing the text to classify and parameters

    Returns:
        Workflow response containing the classification result
    """
    logger.info(
        f"RAG Chain of Thought classification workflow started asynchronously for: {request.text[:100]}..."
    )

    # Step 1: Embed the text
    embedding_request = EmbeddingTaskRequest(
        texts=[request.text],
        model=request.embedding_model,
    )

    embedding_response = await embed_texts_async(embedding_request)

    if not embedding_response.success or not embedding_response.embeddings:
        logger.error("Failed to generate embeddings")
        return RAGChainOfThoughtWorkflowResponse(
            category="Unknown",
            reasoning=embedding_response.error or "Failed to generate embeddings",
        )

    # Step 2: Search for similar examples
    vdb_search_request = VDBSearchRequest(
        query_texts=[request.text],
        query_embeddings=embedding_response.embeddings,
        collection_name=request.collection_name,
        k=request.num_examples,
    )

    vdb_search_response = await search_vdb_async(vdb_search_request)

    if not vdb_search_response.success:
        logger.error(f"Failed to search vector database: {vdb_search_response.error}")
        return RAGChainOfThoughtWorkflowResponse(
            category="Unknown",
            reasoning=vdb_search_response.error or "Failed to search vector database",
        )

    # Get examples from search results
    if not vdb_search_response.results or not vdb_search_response.results[0]:
        logger.warning("No similar examples found")
        examples = []
    else:
        examples = vdb_search_response.results[0]

    # Step 3 & 4: Classify using LLM (prompt creation is now handled inside the LLM task)
    llm_request = ClassificationLLMRequest(
        text=request.text,
        categories=request.categories,
        examples=examples,
        model=request.llm_model,
        max_tokens=request.max_tokens,
        temperature=request.temperature,
    )

    llm_response = await classify_with_llm_async(llm_request)

    if not llm_response.success or not llm_response.response_text:
        logger.error(f"Failed to classify with LLM: {llm_response.error}")
        return RAGChainOfThoughtWorkflowResponse(
            category="Unknown",
            reasoning=llm_response.error or "Failed to classify with LLM",
        )

    # Step 5: Parse the response
    parsed_response = extract_classification_response(
        llm_response.response_text, request.categories
    )

    logger.info(f"Classification result: {parsed_response.get('category')}")

    # Create workflow response
    workflow_response = RAGChainOfThoughtWorkflowResponse(
        category=parsed_response.get("category", "Unknown"),
        reasoning=parsed_response.get("reasoning"),
    )

    logger.info(
        f"RAG Chain of Thought classification workflow completed asynchronously with result: {workflow_response.category}"
    )

    return workflow_response
