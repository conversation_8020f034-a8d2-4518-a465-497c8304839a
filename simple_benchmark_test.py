#!/usr/bin/env python3
"""
Simple test for the benchmarking system core functionality.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_config_loading():
    """Test basic configuration loading."""
    print("Testing configuration loading...")
    
    try:
        from benchmarks.config.benchmark_config import DatasetConfig, DatasetType, MetricType
        
        # Test creating a dataset config
        dataset_config = DatasetConfig(
            name="test_dataset",
            type=DatasetType.REAL,
            path="test.csv",
            categories=["Cat1", "Cat2", "Cat3"]
        )
        
        print(f"✓ Created dataset config: {dataset_config.name}")
        print(f"  Type: {dataset_config.type}")
        print(f"  Categories: {dataset_config.categories}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def test_synthetic_generator():
    """Test synthetic dataset generation."""
    print("\nTesting synthetic dataset generation...")
    
    try:
        from benchmarks.core.synthetic_generator import SyntheticDatasetGenerator
        from benchmarks.config.benchmark_config import DatasetConfig, DatasetType
        
        # Create a simple synthetic dataset config
        dataset_config = DatasetConfig(
            name="test_synthetic",
            type=DatasetType.SYNTHETIC,
            size=5,
            categories=["Billing", "Claims", "Support"],
            domain="insurance",
            text_length_range=(50, 150)
        )
        
        generator = SyntheticDatasetGenerator(seed=42)
        samples = generator.generate_dataset(dataset_config)
        
        print(f"✓ Generated {len(samples)} synthetic samples")
        
        for i, sample in enumerate(samples[:3]):
            print(f"  Sample {i+1}: {sample.text[:60]}... -> {sample.label}")
        
        return True
        
    except Exception as e:
        print(f"✗ Synthetic generator test failed: {e}")
        return False


def test_metrics_collector():
    """Test metrics collection."""
    print("\nTesting metrics collector...")
    
    try:
        from benchmarks.core.metrics_collector import MetricsCollector
        from benchmarks.config.benchmark_config import MetricsConfig, MetricType
        
        # Create simple metrics config
        config = MetricsConfig(
            enabled_metrics=[MetricType.LATENCY, MetricType.ACCURACY],
            monitor_system_resources=False  # Disable to avoid psutil dependency
        )
        
        collector = MetricsCollector(config)
        collector.start_collection()
        
        # Record some test metrics
        collector.record_latency(0.1, {"test": "metric"})
        collector.record_latency(0.2, {"test": "metric"})
        collector.record_accuracy(0.85)
        
        collector.stop_collection()
        
        # Get summary
        summary = collector.get_summary()
        print(f"✓ Collected metrics: {len(summary['metrics'])} types")
        print(f"  Duration: {summary['collection_duration']:.3f}s")
        
        return True
        
    except Exception as e:
        print(f"✗ Metrics collector test failed: {e}")
        return False


async def test_basic_benchmark_runner():
    """Test basic benchmark runner functionality."""
    print("\nTesting benchmark runner...")
    
    try:
        from benchmarks.core.benchmark_runner import BenchmarkRunner
        from benchmarks.workflows.classification_benchmark import create_classification_benchmark_config
        
        # Create a minimal config
        config = create_classification_benchmark_config(
            name="simple_test",
            dataset_configs=[
                {
                    "name": "tiny_synthetic",
                    "type": "synthetic",
                    "size": 3,
                    "categories": ["A", "B"],
                    "domain": "general",
                    "text_length_range": [20, 50]
                }
            ],
            execution_params={
                "max_requests": 3,
                "concurrency_level": 1,
                "warmup_requests": 0,
                "use_mock_services": True,
                "mock_service_delay": 0.001,
            }
        )
        
        runner = BenchmarkRunner(config)
        
        # Register a simple mock workflow
        def simple_workflow(sample, dataset_config, **kwargs):
            return dataset_config.categories[0]  # Always return first category
        
        async def simple_async_workflow(sample, dataset_config, **kwargs):
            await asyncio.sleep(0.001)
            return dataset_config.categories[0]
        
        runner.register_workflow(
            "simple_test_workflow",
            sync_func=simple_workflow,
            async_func=simple_async_workflow
        )
        
        # Update config to use our test workflow
        runner.config.workflow_name = "simple_test_workflow"
        
        print("✓ Created benchmark runner and registered workflow")
        print("✓ Basic benchmark runner test completed")
        
        return True
        
    except Exception as e:
        print(f"✗ Benchmark runner test failed: {e}")
        return False


def main():
    """Run all simple tests."""
    print("Running simple benchmarking system tests...\n")
    
    tests = [
        ("Configuration Loading", test_config_loading),
        ("Synthetic Generator", test_synthetic_generator),
        ("Metrics Collector", test_metrics_collector),
        ("Benchmark Runner", lambda: asyncio.run(test_basic_benchmark_runner())),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results[test_name] = success
        except Exception as e:
            print(f"✗ Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Print summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "PASSED" if success else "FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic tests passed! Core benchmarking system is working.")
    else:
        print(f"⚠️  {total - passed} test(s) failed.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
