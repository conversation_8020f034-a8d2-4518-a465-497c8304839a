#!/usr/bin/env python3
"""
Demonstration of generic stateful routing and VectorDB collection-aware routing.

This script shows how the new generic stateful routing in BaseService works
and how VectorDB service uses it for collection-aware routing to ensure that
all operations on the same collection are consistently routed to the
same VDB instance, maintaining data consistency.
"""

import sys
import os
from typing import Optional

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

try:
    from lego_agent.core.service.base import (
        BaseService,
        BaseServiceRequest,
        BaseServiceResponse,
        BaseServiceInstance,
        BaseServiceInstanceConfig,
    )
    from lego_agent.core.service.vdb import (
        VectorDBService,
        VectorDBServiceRequest,
        VectorDBServiceInstanceConfig,
    )
except ImportError as e:
    print(f"Import error: {e}")
    print("This might be due to missing dependencies")
    sys.exit(1)


class MockServiceInstance(BaseServiceInstance):
    """Mock service instance for demonstration."""

    def __init__(
        self, instance_name: str, group_name: str, config: BaseServiceInstanceConfig
    ):
        super().__init__("demo_service", instance_name, group_name, config)
        self.served_requests = []

    def launch(self):
        pass

    def shutdown(self):
        pass

    def _serve_impl(self, request: BaseServiceRequest) -> BaseServiceResponse:
        self.served_requests.append(request)
        return BaseServiceResponse(
            service_name=self.service_name,
            instance_name=self.instance_name,
            operation_name=request.operation_name,
            raw_data={
                "result": f"served by {self.instance_name}",
                "request_count": len(self.served_requests),
            },
            request_id=request.request_id,
        )

    async def _serve_async_impl(
        self, request: BaseServiceRequest
    ) -> BaseServiceResponse:
        return self._serve_impl(request)


class UserService(BaseService):
    """Example service that uses stateful routing based on user_id."""

    def __init__(self, **kwargs):
        super().__init__(
            service_name="user_service",
            stateful_routing=True,
            stateful_assignment_strategy="consistent_hashing",
            **kwargs,
        )

    def extract_hash_key(self, request: BaseServiceRequest) -> Optional[str]:
        """Extract user_id from request for stateful routing."""
        # First check if hash_key is explicitly set
        if request.hash_key:
            return request.hash_key

        # Extract user_id from raw_data
        raw_data = request.raw_data or {}
        return raw_data.get("user_id")


def create_mock_instance(name: str, group: str = "default") -> MockServiceInstance:
    """Create a mock service instance for demonstration."""
    config = BaseServiceInstanceConfig(tags=frozenset([name]))
    return MockServiceInstance(name, group, config)


def demo_generic_stateful_routing():
    """Demonstrate generic stateful routing with a user service."""
    print("🔧 Generic Stateful Routing Demonstration")
    print("=" * 60)

    # Create user service with stateful routing
    print("\n1. Creating UserService with stateful routing...")
    user_service = UserService()
    print(
        f"   ✓ Service created with stateful routing: {user_service.stateful_routing}"
    )
    print(f"   ✓ Assignment strategy: {user_service.stateful_assignment_strategy}")

    # Add multiple instances
    print("\n2. Adding service instances...")
    instances = {
        "user_server_1": create_mock_instance("user_server_1"),
        "user_server_2": create_mock_instance("user_server_2"),
        "user_server_3": create_mock_instance("user_server_3"),
    }

    for name, instance in instances.items():
        user_service.add_instance(name, instance)
        print(f"   ✓ Added {name}")

    # Test user-based routing consistency
    print("\n3. Testing user-based routing consistency...")
    users = ["alice", "bob", "charlie", "diana"]

    user_assignments = {}

    for user in users:
        # Create multiple requests for the same user
        requests = []
        for i in range(3):
            request = BaseServiceRequest(
                service_name="user_service",
                operation_name="process_user_data",
                raw_data={
                    "user_id": user,
                    "action": f"action_{i}",
                    "data": f"user data for {user}",
                },
            )
            requests.append(request)

        # Schedule all requests and check they go to the same instance
        assigned_instances = []
        for request in requests:
            instance = user_service.schedule(request)
            assigned_instances.append(instance.instance_name)

        # Verify consistency
        unique_instances = set(assigned_instances)
        if len(unique_instances) == 1:
            assigned_instance = list(unique_instances)[0]
            user_assignments[user] = assigned_instance
            print(f"   ✓ User '{user}' → {assigned_instance} (consistent)")
        else:
            print(f"   ✗ User '{user}' → {assigned_instances} (inconsistent!)")

    # Show user-to-instance mapping
    print("\n4. User-to-instance mapping:")
    mapping = user_service.get_hash_key_mapping()
    for user, instance in mapping.items():
        print(f"   {user} → {instance}")

    # Test explicit mapping
    print("\n5. Testing explicit user mapping...")
    explicit_user = "admin"
    target_instance = "user_server_2"

    user_service.set_hash_key_mapping(explicit_user, target_instance)
    print(f"   Explicitly mapped '{explicit_user}' → {target_instance}")

    # Verify the mapping works
    request = BaseServiceRequest(
        service_name="user_service",
        operation_name="admin_action",
        raw_data={"user_id": explicit_user, "action": "admin_task"},
    )

    instance = user_service.schedule(request)
    if instance.instance_name == target_instance:
        print(
            f"   ✓ Request routed to explicitly mapped instance: {instance.instance_name}"
        )
    else:
        print(f"   ✗ Request routed to wrong instance: {instance.instance_name}")

    return user_service


def demo_vdb_collection_routing():
    """Demonstrate VectorDB collection-aware routing."""
    print("\n\n🗄️  VectorDB Collection-Aware Routing Demonstration")
    print("=" * 60)

    # Create VectorDB service (automatically enables stateful routing)
    print("\n1. Creating VectorDBService with collection-aware routing...")
    vdb_service = VectorDBService(collection_assignment_strategy="consistent_hashing")
    print(f"   ✓ Service created with stateful routing: {vdb_service.stateful_routing}")
    print(
        f"   ✓ Collection assignment strategy: {vdb_service.stateful_assignment_strategy}"
    )

    # Add VDB instances (using mock instances for demo)
    print("\n2. Adding VDB instances...")
    vdb_instances = {
        "vdb_instance_1": create_mock_instance("vdb_instance_1"),
        "vdb_instance_2": create_mock_instance("vdb_instance_2"),
        "vdb_instance_3": create_mock_instance("vdb_instance_3"),
    }

    for name, instance in vdb_instances.items():
        vdb_service.add_instance(name, instance)
        print(f"   ✓ Added {name}")

    # Test collection assignment consistency
    print("\n3. Testing collection assignment consistency...")
    collections = ["user_docs", "product_catalog", "support_tickets", "knowledge_base"]

    collection_assignments = {}

    for collection in collections:
        # Create multiple requests for the same collection
        requests = []
        for i in range(3):
            request = VectorDBServiceRequest(
                service_name="vdb",
                operation_name="store",
                raw_data={
                    "collection_name": collection,
                    "texts": [f"Document {i} for {collection}"],
                    "embeddings": [[0.1 * (i + 1)] * 10],
                },
            )
            requests.append(request)

        # Schedule all requests and check they go to the same instance
        assigned_instances = []
        for request in requests:
            instance = vdb_service.schedule(request)
            assigned_instances.append(instance.instance_name)

        # Verify consistency
        unique_instances = set(assigned_instances)
        if len(unique_instances) == 1:
            assigned_instance = list(unique_instances)[0]
            collection_assignments[collection] = assigned_instance
            print(f"   ✓ Collection '{collection}' → {assigned_instance} (consistent)")
        else:
            print(
                f"   ✗ Collection '{collection}' → {assigned_instances} (inconsistent!)"
            )

    # Show collection mapping using both generic and VDB-specific methods
    print("\n4. Collection-to-instance mapping:")
    mapping = vdb_service.get_collection_mapping()  # VDB-specific method
    generic_mapping = vdb_service.get_hash_key_mapping()  # Generic method

    print("   Using VDB-specific method:")
    for collection, instance in mapping.items():
        print(f"     {collection} → {instance}")

    print("   Using generic method (should be identical):")
    for hash_key, instance in generic_mapping.items():
        print(f"     {hash_key} → {instance}")

    # Verify they're the same
    assert (
        mapping == generic_mapping
    ), "VDB-specific and generic mappings should be identical"
    print("   ✓ VDB-specific and generic mappings are identical")

    # Test search-after-store consistency
    print("\n5. Testing search-after-store consistency...")
    test_collection = "consistency_test"

    # Store operation
    store_request = VectorDBServiceRequest(
        service_name="vdb",
        operation_name="store",
        raw_data={
            "collection_name": test_collection,
            "texts": ["Important document"],
            "embeddings": [[0.5] * 10],
        },
    )

    store_instance = vdb_service.schedule(store_request)
    print(f"   Store request → {store_instance.instance_name}")

    # Search operation
    search_request = VectorDBServiceRequest(
        service_name="vdb",
        operation_name="search",
        raw_data={
            "collection_name": test_collection,
            "query_texts": ["Important"],
            "query_embeddings": [[0.5] * 10],
            "k": 5,
        },
    )

    search_instance = vdb_service.schedule(search_request)
    print(f"   Search request → {search_instance.instance_name}")

    if store_instance.instance_name == search_instance.instance_name:
        print(
            "   ✓ Store and search routed to same instance (data consistency maintained)"
        )
    else:
        print(
            "   ✗ Store and search routed to different instances (data inconsistency!)"
        )

    return vdb_service


def main():
    print("🚀 Stateful Routing Demonstration")
    print("This demo shows the generic stateful routing capabilities")
    print("and how VectorDB service uses them for collection-aware routing.")

    # Demo 1: Generic stateful routing with user service
    user_service = demo_generic_stateful_routing()

    # Demo 2: VectorDB collection-aware routing
    vdb_service = demo_vdb_collection_routing()

    print("\n" + "=" * 60)
    print("🎉 Stateful routing demonstration completed!")
    print("\nKey benefits demonstrated:")
    print("• Generic stateful routing: Any service can use hash-based routing")
    print("• Consistent routing: Same hash key always goes to same instance")
    print("• Data consistency: Related operations use same instance")
    print("• Multiple strategies: Consistent hashing, load balancing, explicit")
    print("• Service-specific extraction: Each service defines its hash key logic")
    print("• Backward compatibility: VDB service maintains existing API")
    print("• Flexible and reusable: Pattern can be used by any stateful service")


if __name__ == "__main__":
    main()
